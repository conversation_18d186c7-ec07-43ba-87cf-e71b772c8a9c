// models/Report.js
const mongoose = require('mongoose');

const ReportSchema = new mongoose.Schema({
  reporterId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  reportedUserId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  reason: { type: String, required: true, maxlength: 200 },
  details: { type: String, default: '', maxlength: 2000 },
  status: { type: String, enum: ['open', 'resolved'], default: 'open' },
  resolution: {
    action: { type: String, enum: ['warning', 'block', 'ignore', 'other'], default: null },
    notes: { type: String, default: '' },
    resolvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', default: null },
    resolvedAt: { type: Date, default: null }
  },
  createdAt: { type: Date, default: Date.now }
});

ReportSchema.index({ status: 1, createdAt: -1 });
ReportSchema.index({ reportedUserId: 1, createdAt: -1 });
ReportSchema.index({ reporterId: 1, createdAt: -1 });

module.exports = mongoose.model('Report', ReportSchema);

