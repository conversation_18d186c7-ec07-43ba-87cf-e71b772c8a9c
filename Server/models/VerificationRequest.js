// models/VerificationRequest.js
const mongoose = require('mongoose');

const VerificationRequestSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    unique: true // Only one active request per user
  },
  username: { 
    type: String, 
    required: true 
  },
  verificationPhoto: {
    type: String,
    required: true // Base64 encoded image or image URL
  },
  requiredFingers: {
    type: Number,
    required: false, // Made optional for backward compatibility with existing records
    min: 1,
    max: 5,
    default: null
  },
  status: { 
    type: String, 
    enum: ['pending', 'approved', 'rejected'], 
    default: 'pending' 
  },
  submittedAt: { 
    type: Date, 
    default: Date.now 
  },
  reviewedAt: { 
    type: Date, 
    default: null 
  },
  reviewedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    default: null 
  },
  reviewNotes: { 
    type: String, 
    default: '', 
    maxlength: 1000 
  },
  rejectionReason: { 
    type: String, 
    enum: [
      'photo_unclear', 
      'photo_inappropriate', 
      'identity_mismatch', 
      'fake_document', 
      'other'
    ], 
    default: null 
  }
});

// Indexes for efficient queries
VerificationRequestSchema.index({ status: 1, submittedAt: -1 });
VerificationRequestSchema.index({ userId: 1 });
VerificationRequestSchema.index({ reviewedBy: 1, reviewedAt: -1 });

// Virtual to check if request is still pending
VerificationRequestSchema.virtual('isPending').get(function() {
  return this.status === 'pending';
});

// Virtual to check if request was approved
VerificationRequestSchema.virtual('isApproved').get(function() {
  return this.status === 'approved';
});

// Virtual to check if request was rejected
VerificationRequestSchema.virtual('isRejected').get(function() {
  return this.status === 'rejected';
});

// Ensure virtual fields are included when converting to JSON
VerificationRequestSchema.set('toJSON', {
  virtuals: true,
  transform: (doc, ret) => {
    ret.id = ret._id.toString();
    return ret;
  }
});

module.exports = mongoose.model('VerificationRequest', VerificationRequestSchema);
