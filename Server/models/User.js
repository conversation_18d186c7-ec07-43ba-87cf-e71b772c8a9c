// models/User.js
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  // MongoDB automatically creates _id field
  // We don't need to manually define it

  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  email: {
    type: String,
    sparse: true, // Allow multiple null values but unique non-null values
    lowercase: true,
    trim: true
  },
  socialAuth: {
    google: {
      id: String,
      email: String,
      name: String,
      photo: String
    },
    apple: {
      id: String,
      email: String,
      name: String
    },
    facebook: {
      id: String,
      email: String,
      name: String,
      photo: String
    }
  },
  age: {
    type: Number,
    min: 18,
    max: 120
  },
  description: {
    type: String,
    maxlength: 500,
    default: ""
  },
  passions: {
    type: [String],
    default: []
  },
  images: {
    type: [String], // Array of image URLs or base64 encoded images
    default: [],
    validate: [arrayLimit, '{PATH} exceeds the limit of 4 images']
  },
  // Gender and matching preferences
  gender: {
    type: String,
    enum: ['male', 'female'],
    default: null
  },
  interestedIn: {
    type: String,
    enum: ['male', 'female', 'both'],
    default: 'both'
  },
  lookingFor: {
    type: String,
    enum: ['relationship', 'friends'],
    default: 'relationship'
  },
  // Admin and account management fields
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  accountStatus: {
    type: String,
    enum: ['active', 'blocked', 'deleted'],
    default: 'active'
  },
  lastLogin: {
    type: Date,
    default: null
  },
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: {
    type: Date,
    default: null
  },
  // Moderation: warnings issued to this user
  warnings: [{
    reason: { type: String, required: true, maxlength: 200 },
    notes: { type: String, default: '', maxlength: 1000 },
    issuedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    issuedAt: { type: Date, default: Date.now }
  }],
  warningCount: { type: Number, default: 0 },

  // Premium subscription fields
  premium: {
    isActive: { type: Boolean, default: false },
    subscriptionType: {
      type: String,
      enum: ['monthly', 'yearly'],
      default: null
    },
    subscriptionStart: { type: Date, default: null },
    subscriptionEnd: { type: Date, default: null },
    grantedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', default: null }, // Admin who granted premium
    grantedAt: { type: Date, default: null },
    autoRenew: { type: Boolean, default: false },
    paymentMethod: { type: String, default: null }, // For future payment integration
    customLocation: {
      enabled: { type: Boolean, default: false },
      city: { type: String, default: null },
      country: { type: String, default: null },
      coordinates: {
        type: { type: String, enum: ['Point'], default: 'Point' },
        coordinates: { type: [Number], default: null } // [longitude, latitude]
      },
      lastUpdated: { type: Date, default: null }
    }
  },

  // Verification fields
  verification: {
    isVerified: { type: Boolean, default: false },
    verifiedAt: { type: Date, default: null },
    verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', default: null }
  },

  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Add virtual getter for id that returns _id as string
userSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// Virtual field to check if account is locked
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Virtual field to check if user is online (based on lastLogin within last 5 minutes)
userSchema.virtual('isOnline').get(function() {
  if (!this.lastLogin) return false;
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.lastLogin > fiveMinutesAgo;
});

// Virtual field to check if user has active premium subscription
userSchema.virtual('isPremium').get(function() {
  if (!this.premium.isActive) return false;

  // If no end date is set, consider it active (manually granted by admin)
  if (!this.premium.subscriptionEnd) return true;

  // Check if subscription hasn't expired
  return this.premium.subscriptionEnd > new Date();
});

// Virtual field to get days remaining in premium subscription
userSchema.virtual('premiumDaysRemaining').get(function() {
  if (!this.isPremium) return 0;
  if (!this.premium.subscriptionEnd) return -1; // Unlimited (admin granted)

  const now = new Date();
  const endDate = new Date(this.premium.subscriptionEnd);
  const diffTime = endDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
});

// Virtual field to check if user is verified
userSchema.virtual('isVerified').get(function() {
  return this.verification.isVerified;
});

// Virtual field to get badge type based on verification and subscription
userSchema.virtual('badgeType').get(function() {
  if (!this.verification.isVerified) {
    return null;
  }

  // Gold badge for verified users with active premium subscription
  if (this.premium && this.premium.isActive) {
    return 'gold';
  }

  // Blue badge for verified users without premium
  return 'blue';
});

// Ensure virtual fields are included when converting to JSON
userSchema.set('toJSON', {
  virtuals: true,
  transform: (doc, ret) => {
    ret.id = ret._id.toString();
    // Don't include sensitive fields in JSON output
    delete ret.password;
    delete ret.loginAttempts;
    delete ret.lockUntil;
    return ret;
  }
});

// Update the updatedAt field before saving
userSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }

  const updates = { $inc: { loginAttempts: 1 } };

  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }

  return this.updateOne(updates);
};

// Instance method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { lastLogin: new Date() }
  });
};

// Instance method to grant premium subscription
userSchema.methods.grantPremium = function(subscriptionType = null, durationMonths = null, grantedByUserId = null) {
  const updates = {
    'premium.isActive': true,
    'premium.grantedAt': new Date()
  };

  if (grantedByUserId) {
    updates['premium.grantedBy'] = grantedByUserId;
  }

  if (subscriptionType && durationMonths) {
    updates['premium.subscriptionType'] = subscriptionType;
    updates['premium.subscriptionStart'] = new Date();

    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + durationMonths);
    updates['premium.subscriptionEnd'] = endDate;
  }

  return this.updateOne({ $set: updates });
};

// Instance method to revoke premium subscription
userSchema.methods.revokePremium = function() {
  return this.updateOne({
    $set: {
      'premium.isActive': false,
      'premium.subscriptionEnd': new Date(), // Set to now to expire immediately
      'premium.customLocation.enabled': false // Disable custom location when premium is revoked
    }
  });
};

// Instance method to update custom location (premium feature)
userSchema.methods.updateCustomLocation = function(city, country, coordinates) {
  if (!this.isPremium) {
    throw new Error('Custom location is a premium feature');
  }

  return this.updateOne({
    $set: {
      'premium.customLocation.enabled': true,
      'premium.customLocation.city': city,
      'premium.customLocation.country': country,
      'premium.customLocation.coordinates.coordinates': coordinates,
      'premium.customLocation.lastUpdated': new Date()
    }
  });
};

// Instance method to disable custom location
userSchema.methods.disableCustomLocation = function() {
  return this.updateOne({
    $set: {
      'premium.customLocation.enabled': false,
      'premium.customLocation.city': null,
      'premium.customLocation.country': null,
      'premium.customLocation.coordinates.coordinates': null,
      'premium.customLocation.lastUpdated': new Date()
    }
  });
};

// Validator to limit array length to 4
function arrayLimit(val) {
  return val.length <= 4;
}

module.exports = mongoose.model('User', userSchema);