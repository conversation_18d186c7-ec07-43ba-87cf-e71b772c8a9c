// clear-database.js - Uses your existing models to clear the database
const mongoose = require('mongoose');
const User = require('./models/User');
const Match = require('./models/Match');
const Message = require('./models/Message');

// Same connection string as your server
mongoose.connect('mongodb://localhost:27017/shakeAndMatch', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('Connected to MongoDB'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function clearDatabase() {
  try {
    // Clear all collections
    const userResult = await User.deleteMany({});
    console.log(`Deleted ${userResult.deletedCount} users`);
    
    const matchResult = await Match.deleteMany({});
    console.log(`Deleted ${matchResult.deletedCount} matches`);
    
    const messageResult = await Message.deleteMany({});
    console.log(`Deleted ${messageResult.deletedCount} messages`);
    
    console.log('All collections cleared successfully');
  } catch (error) {
    console.error('Error clearing database:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
}

// Run the function
clearDatabase();
