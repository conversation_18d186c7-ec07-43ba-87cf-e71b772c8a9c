<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Shake & Match</title>
    <link rel="stylesheet" href="/admin/assets/admin-dashboard.css">
    <!-- SVG Sprite for Icons -->
    <svg xmlns="http://www.w3.org/2000/svg" style="display:none">
        <symbol id="icon-search" viewBox="0 0 24 24" fill="currentColor">
            <path d="M10 3a7 7 0 0 1 5.292 11.708l4 4a1 1 0 0 1-1.414 1.414l-4-4A7 7 0 1 1 10 3zm0 2a5 5 0 1 0 0 10A5 5 0 0 0 10 5z"/>
        </symbol>
        <symbol id="icon-bell" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2a4 4 0 0 1 4 4v1.09c2.282 1.167 4 3.757 4 6.91v2l2 2v1H2v-1l2-2v-2c0-3.153 1.718-5.743 4-6.91V6a4 4 0 0 1 4-4zm0 20a3 3 0 0 1-3-3h6a3 3 0 0 1-3 3z"/>
        </symbol>
        <symbol id="icon-gear" viewBox="0 0 24 24" fill="currentColor">
            <path d="M11.25 1.5h1.5l.84 1.68c.2.4.62.66 1.07.66h1.9l.42 1.64-1.46 1.06c-.38.27-.54.77-.39 1.22l.6 1.84-1.06 1.06-1.84-.6c-.45-.15-.95.01-1.22.39L12 14.46l-1.5-.01-1.06-1.06c-.27-.38-.77-.54-1.22-.39l-1.84.6-1.06-1.06.6-1.84c.15-.45-.01-.95-.39-1.22L3.07 5.48 3.49 3.84h1.9c.45 0 .87-.26 1.07-.66L7.3 1.5h1.5l.61 1.22c.2.4.62.66 1.07.66.45 0 .87-.26 1.07-.66L11.25 1.5zM12 9a3 3 0 110 6 3 3 0 010-6z"/>
        </symbol>
        <symbol id="icon-users" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16 11c1.657 0 3-1.79 3-4s-1.343-4-3-4-3 1.79-3 4 1.343 4 3 4zm-8 0c1.657 0 3-1.79 3-4S9.657 3 8 3 5 4.79 5 7s1.343 4 3 4zm0 2c-3.314 0-6 2.239-6 5v2h10v-2c0-2.761-2.686-5-6-5zm8 0c-.739 0-1.427.113-2.047.316 1.976 1.007 3.047 2.56 3.047 4.684V20h7v-2c0-2.761-2.686-5-6-5z"/>
        </symbol>
        <symbol id="icon-heart" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 21s-8-5.686-8-11a5 5 0 0 1 9-3 5 5 0 0 1 9 3c0 5.314-8 11-8 11z"/>
        </symbol>
        <symbol id="icon-mail" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm0 4-8 5-8-5V6l8 5 8-5v2z"/>
        </symbol>
        <symbol id="icon-eye" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 5c-7 0-11 7-11 7s4 7 11 7 11-7 11-7-4-7-11-7zm0 12a5 5 0 1 1 0-10 5 5 0 0 1 0 10z"/>
        </symbol>
        <symbol id="icon-pencil" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04a1 1 0 0 0 0-1.41L18.37 3.29a1 1 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
        </symbol>
        <symbol id="icon-key" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14 3a5 5 0 1 0 3.546 8.546L22 16v3h-3v3h-3v-3h-3v-3l2.454-2.454A5 5 0 0 0 14 3z"/>
        </symbol>
        <symbol id="icon-dashboard" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 3h8v8H3V3zm10 0h8v8h-8V3zM3 13h8v8H3v-8zm10 0h8v8h-8v-8z"/>
        </symbol>
        <symbol id="icon-chart" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 3h2v18H3V3zm4 10h2v8H7v-8zm4-6h2v14h-2V7zm4 4h2v10h-2V11zm4-8h2v18h-2V3z"/>
        </symbol>
        <symbol id="icon-logout" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16 13v-2H7V8l-5 4 5 4v-3h9zm3-10H11a2 2 0 0 0-2 2v3h2V5h8v14h-8v-3H9v3a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z"/>
        </symbol>
        <symbol id="icon-star" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </symbol>
        <symbol id="icon-alert" viewBox="0 0 24 24" fill="currentColor">
            <path d="M10.29 3.86L1.82 18a2 2 0 001.71 3h16.94a2 2 0 001.71-3L13.71 3.86a2 2 0 00-3.42 0zM12 9v4m0 4h.01"/>
        </symbol>
        <symbol id="icon-shield-check" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2L3 7v5c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-9-5zm-2 13l-4-4 1.41-1.41L10 12.17l6.59-6.59L18 7l-8 8z"/>
        </symbol>
    </svg>

</head>

<body>
    <div class="app-layout">
        <!-- Enhanced Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon"></div>
                    <div class="logo-text">Shake&Match</div>
                </div>
            </div>
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <button class="nav-item active" onclick="showSection('dashboard', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-dashboard"/></svg></div>
                        Dashboard
                    </button>
                    <button class="nav-item" onclick="showSection('analytics', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-chart"/></svg></div>
                        Analytics
                    </button>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <button class="nav-item" onclick="showSection('users', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-users"/></svg></div>
                        Users
                    </button>
                    <button class="nav-item" onclick="showSection('matches', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-heart"/></svg></div>
                        Matches
                    </button>
                    <button class="nav-item" onclick="showSection('messages', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-mail"/></svg></div>
                        Messages
                    </button>
                    <button class="nav-item" onclick="showSection('reports', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-alert"/></svg></div>
                        Reports
                    </button>
                    <button class="nav-item" onclick="showSection('verification', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-shield-check"/></svg></div>
                        Verification
                    </button>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Settings</div>
                    <button class="nav-item" onclick="showSection('profile', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-users"/></svg></div>
                        Profile
                    </button>
                    <button class="nav-item" onclick="showSection('settings', this)">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-gear"/></svg></div>
                        Settings
                    </button>
                    <button class="nav-item" onclick="logout()">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-logout"/></svg></div>
                        Logout
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Enhanced Top Bar -->
            <div class="top-bar">
                <div class="page-title">
                    <h1>Dashboard</h1>
                    <div class="breadcrumb">
                        <span>Admin</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current">Dashboard</span>
                    </div>
                </div>

                <div class="search-bar">
                    <span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-search"/></svg></span>
                    <input type="text" class="search-input" placeholder="Search..." aria-label="Search">
                </div>

                <div class="top-actions">
                    <button class="action-btn" title="Notifications" aria-label="Notifications">
                        <span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-bell"/></svg></span>
                        <span class="notification-dot"></span>
                    </button>
                    <button class="action-btn" title="Settings" aria-label="Settings">
                        <span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-gear"/></svg></span>
                    </button>
                    <div class="user-profile">
                        <div class="user-avatar" id="userAvatar">A</div>
                        <div class="user-info">
                            <div class="user-name" id="currentUsername">Admin</div>
                            <div class="user-role">Administrator</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-area">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="content-section">


                    <!-- Enhanced Stats Grid -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Active Users</div>
                                <div class="stat-icon active" aria-hidden="true"><svg><use href="#icon-users"/></svg></div>
                            </div>
                            <div class="stat-value" id="activeUsers">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span>12%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Total Users</div>
                                <div class="stat-icon users" aria-hidden="true"><svg><use href="#icon-users"/></svg></div>
                            </div>
                            <div class="stat-value" id="totalUsers">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span>18%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Total Matches</div>
                                <div class="stat-icon revenue" aria-hidden="true"><svg><use href="#icon-heart"/></svg></div>
                            </div>
                            <div class="stat-value" id="totalMatches">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span>24%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Total Messages</div>
                                <div class="stat-icon growth" aria-hidden="true"><svg><use href="#icon-mail"/></svg></div>
                            </div>
                            <div class="stat-value" id="totalMessages">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span>35%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Premium Users</div>
                                <div class="stat-icon premium" aria-hidden="true" style="color: var(--accent-warning);"><svg><use href="#icon-star"/></svg></div>
                            </div>
                            <div class="stat-value" id="premiumUsers">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span id="premiumUsersChange">0%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">active subscriptions</span>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="charts-row">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">Monthly Activity</h3>
                                <div class="chart-options">
                                    <button class="chart-option-btn active">Week</button>
                                    <button class="chart-option-btn">Month</button>
                                    <button class="chart-option-btn">Year</button>
                                </div>
                            </div>
                            <div class="chart-container" id="activityChart">
                                <div class="bar-chart">
                                    <div class="bar" style="height: 40%;"></div>
                                    <div class="bar" style="height: 70%;"></div>
                                    <div class="bar" style="height: 60%;"></div>
                                    <div class="bar" style="height: 90%;"></div>
                                    <div class="bar" style="height: 50%;"></div>
                                    <div class="bar" style="height: 45%;"></div>
                                    <div class="bar" style="height: 65%;"></div>
                                    <div class="bar" style="height: 55%;"></div>
                                    <div class="bar" style="height: 40%;"></div>
                                </div>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">User Distribution</h3>
                            </div>
                            <div class="circular-chart">
                                <div class="progress-ring">
                                    <svg>
                                        <defs>
                                            <linearGradient id="gradientChart" x1="0%" y1="0%" x2="100%" y2="0%">
                                                <stop offset="0%" style="stop-color: var(--accent-primary)" />
                                                <stop offset="50%" style="stop-color: var(--accent-secondary)" />
                                                <stop offset="100%" style="stop-color: var(--accent-purple)" />
                                            </linearGradient>
                                        </defs>
                                        <circle class="progress-bg" cx="90" cy="90" r="80"></circle>
                                        <circle class="progress-fill" cx="90" cy="90" r="80"></circle>
                                    </svg>
                                    <div class="progress-text">
                                        <div class="progress-value">68%</div>
                                        <div class="progress-label">Active</div>
                                    </div>
                                </div>
                                <div class="device-legend">
                                    <div class="legend-item">
                                        <div class="legend-label">
                                            <span class="legend-dot" style="background: var(--accent-primary);"></span>
                                            Active Users
                                        </div>
                                        <div class="legend-value">68%</div>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-label">
                                            <span class="legend-dot" style="background: var(--accent-secondary);"></span>
                                            Inactive Users
                                        </div>
                                        <div class="legend-value">25%</div>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-label">
                                            <span class="legend-dot" style="background: var(--accent-purple);"></span>
                                            Blocked Users
                                        </div>
                                        <div class="legend-value">7%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Section -->
                <div id="users-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <h2>User Management</h2>
                            <div class="chart-options">
                                <input type="text" class="search-input" placeholder="Search users..." id="userSearch" onkeyup="searchUsers()">
                                <select id="roleFilter" class="form-select" onchange="filterUsers()">
                                    <option value="">All Roles</option>
                                    <option value="user">User</option>
                                    <option value="admin">Admin</option>
                                </select>
                                <select id="statusFilter" class="form-select" onchange="filterUsers()">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="blocked">Blocked</option>
                                    <option value="deleted">Deleted</option>
                                </select>
                                <select id="premiumFilter" class="form-select" onchange="filterUsers()">
                                    <option value="">All Users</option>
                                    <option value="premium">Premium</option>
                                    <option value="regular">Regular</option>
                                </select>
                                <button class="btn btn-primary" onclick="showCreateUserModal()">
                                    + Add User
                                </button>
                                <button class="btn btn-secondary" onclick="showBulkActionsModal()" id="bulkActionsBtn" style="display: none;">
                                    Bulk Actions
                                </button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="selectAllUsers" onchange="toggleSelectAll()"></th>
                                        <th onclick="sortUsers('username')">Username ↕</th>
                                        <th onclick="sortUsers('email')">Email ↕</th>
                                        <th onclick="sortUsers('role')">Role ↕</th>
                                        <th onclick="sortUsers('accountStatus')">Status ↕</th>
                                        <th onclick="sortUsers('premium.isActive')">Premium ↕</th>
                                        <th onclick="sortUsers('verification.isVerified')">Verified ↕</th>
                                        <th onclick="sortUsers('createdAt')">Created ↕</th>
                                        <th>Images</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td colspan="9" style="text-align: center; color: var(--text-muted);">Loading users...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-container" id="usersPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="usersInfo">Showing 0 of 0 users</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" onclick="changePage(-1)" id="prevPageBtn">Previous</button>
                                <span id="pageInfo">Page 1 of 1</span>
                                <button class="btn btn-secondary" onclick="changePage(1)" id="nextPageBtn">Next</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Matches Section -->
                <div id="matches-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <h2>Match Management</h2>
                            <div class="chart-options">
                                <input type="text" class="search-input" placeholder="Search matches..." id="matchSearch" onkeyup="searchMatches()">
                                <button class="btn btn-secondary" onclick="refreshMatches()">Refresh</button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Match ID</th>
                                        <th>User 1</th>
                                        <th>User 2</th>
                                        <th>Distance (km)</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="matchesTableBody">
                                    <tr>
                                        <td colspan="6" style="text-align: center; color: var(--text-muted);">Loading matches...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-container" id="matchesPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="matchesInfo">Showing 0 of 0 matches</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" onclick="changeMatchesPage(-1)" id="prevMatchesPageBtn">Previous</button>

                                <span id="matchesPageInfo">Page 1 of 1</span>
                                <button class="btn btn-secondary" onclick="changeMatchesPage(1)" id="nextMatchesPageBtn">Next</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Messages Section -->
                <div id="messages-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <h2>Message Management</h2>
                            <div class="chart-options">
                                <input type="text" class="search-input" placeholder="Search messages..." id="messageSearch" onkeyup="searchMessages()">
                                <button class="btn btn-secondary" onclick="refreshMessages()">Refresh</button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Message ID</th>
                                        <th>From</th>
                                        <th>To</th>
                                        <th>Message</th>
                                        <th>Timestamp</th>
                                        <th>Read</th>
                                    </tr>
                                </thead>
                                <tbody id="messagesTableBody">
                                    <tr>
                                        <td colspan="6" style="text-align: center; color: var(--text-muted);">Loading messages...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-container" id="messagesPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="messagesInfo">Showing 0 of 0 messages</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" onclick="changeMessagesPage(-1)" id="prevMessagesPageBtn">Previous</button>
                                <span id="messagesPageInfo">Page 1 of 1</span>
                                <button class="btn btn-secondary" onclick="changeMessagesPage(1)" id="nextMessagesPageBtn">Next</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div id="reports-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <div>
                                <h2 class="section-title">User Reports</h2>
                                <p class="section-subtitle">Review, warn, or block reported users</p>
                            </div>
                            <div class="actions" style="display:flex; gap:.5rem; align-items:center;">
                                <select id="reportsStatusFilter" class="input" style="width:auto">
                                    <option value="open">Open</option>
                                    <option value="resolved">Resolved</option>
                                    <option value="all">All</option>
                                </select>
                                <button class="btn btn-primary" onclick="loadReports(1)">Refresh</button>
                                <button class="btn btn-success" onclick="testReportsAPI()">Test API</button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Reported User</th>
                                        <th>Reporter</th>
                                        <th>Reason</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="reportsTableBody">
                                    <tr><td colspan="6" style="text-align:center;color:var(--text-muted)">Loading reports...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination" id="reportsPagination" style="display:flex;gap:.5rem;justify-content:flex-end;margin-top:1rem;">
                            <button class="btn btn-secondary" onclick="changeReportsPage(-1)">Prev</button>
                            <span id="reportsPageInfo" style="align-self:center"></span>
                            <button class="btn btn-secondary" onclick="changeReportsPage(1)">Next</button>
                        </div>
                    </div>
                </div>

                <!-- Verification Section -->
                <div id="verification-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <div>
                                <h2 class="section-title">Account Verification</h2>
                                <p class="section-subtitle">Review and manage user verification requests</p>
                            </div>
                            <div class="actions" style="display:flex; gap:.5rem; align-items:center;">
                                <select id="verificationStatusFilter" class="input" style="width:auto">
                                    <option value="pending">Pending</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="all">All</option>
                                </select>
                                <button class="btn btn-primary" onclick="loadVerificationRequests(1)">Refresh</button>
                            </div>
                        </div>

                        <!-- Verification Stats -->
                        <div class="stats-grid" style="margin-bottom: 2rem;">
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-title">Pending Requests</div>
                                    <div class="stat-icon warning" aria-hidden="true"><svg><use href="#icon-shield-check"/></svg></div>
                                </div>
                                <div class="stat-value" id="verificationPendingCount">0</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-title">Verified Users</div>
                                    <div class="stat-icon success" aria-hidden="true"><svg><use href="#icon-shield-check"/></svg></div>
                                </div>
                                <div class="stat-value" id="verificationVerifiedCount">0</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-title">Blue Badges</div>
                                    <div class="stat-icon info" aria-hidden="true"><svg><use href="#icon-shield-check"/></svg></div>
                                </div>
                                <div class="stat-value" id="verificationBlueCount">0</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-title">Gold Badges</div>
                                    <div class="stat-icon premium" aria-hidden="true"><svg><use href="#icon-star"/></svg></div>
                                </div>
                                <div class="stat-value" id="verificationGoldCount">0</div>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Submitted</th>
                                        <th>Status</th>
                                        <th>Reviewed By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="verificationTableBody">
                                    <tr><td colspan="5" style="text-align:center;color:var(--text-muted)">Loading verification requests...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination" id="verificationPagination" style="display:flex;gap:.5rem;justify-content:flex-end;margin-top:1rem;">
                            <button class="btn btn-secondary" onclick="changeVerificationPage(-1)">Prev</button>
                            <span id="verificationPageInfo" style="align-self:center"></span>
                            <button class="btn btn-secondary" onclick="changeVerificationPage(1)">Next</button>
                        </div>
                    </div>
                </div>

                <!-- Analytics Section -->
                <div id="analytics-section" class="content-section" style="display: none;">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Active Users (Online)</div>
                                <div class="stat-icon active" aria-hidden="true"><svg><use href="#icon-users"/></svg></div>
                            </div>
                            <div class="stat-value" id="analyticsActiveUsers">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Recent Matches (24h)</div>
                                <div class="stat-icon revenue" aria-hidden="true"><svg><use href="#icon-heart"/></svg></div>
                            </div>
                            <div class="stat-value" id="analyticsRecentMatches">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Recent Messages (24h)</div>
                                <div class="stat-icon growth" aria-hidden="true"><svg><use href="#icon-mail"/></svg></div>
                            </div>
                            <div class="stat-value" id="analyticsRecentMessages">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">New Users (24h)</div>
                                <div class="stat-icon users" aria-hidden="true"><svg><use href="#icon-users"/></svg></div>
                            </div>
                            <div class="stat-value" id="analyticsNewUsers">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div id="createUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Create New User</h3>
                <span class="close" onclick="closeModal('createUserModal')">&times;</span>
            </div>
            <form id="createUserForm">
                <div class="form-group">
                    <label class="form-label" for="newUsername">Username *</label>
                    <input type="text" id="newUsername" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="newEmail">Email</label>
                    <input type="email" id="newEmail" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label" for="newPassword">Password *</label>
                    <input type="password" id="newPassword" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="newRole">Role</label>
                    <select id="newRole" class="form-select">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="newAge">Age</label>
                    <input type="number" id="newAge" class="form-input" min="18" max="120">
                </div>
                <div class="form-group">
                    <label class="form-label" for="newDescription">Description</label>
                    <textarea id="newDescription" class="form-textarea" placeholder="User description..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('createUserModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Edit User</h3>
                <span class="close" onclick="closeModal('editUserModal')">&times;</span>
            </div>
            <form id="editUserForm">
                <input type="hidden" id="editUserId">
                <div class="form-group">
                    <label class="form-label" for="editUsername">Username *</label>
                    <input type="text" id="editUsername" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="editEmail">Email</label>
                    <input type="email" id="editEmail" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label" for="editRole">Role</label>
                    <select id="editRole" class="form-select">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="editAccountStatus">Account Status</label>
                    <select id="editAccountStatus" class="form-select">
                        <option value="active">Active</option>
                        <option value="blocked">Blocked</option>
                        <option value="deleted">Deleted</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="editAge">Age</label>
                    <input type="number" id="editAge" class="form-input" min="18" max="120">
                </div>
                <div class="form-group">
                    <label class="form-label" for="editDescription">Description</label>
                    <textarea id="editDescription" class="form-textarea" placeholder="User description..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-danger" onclick="deleteUser(document.getElementById('editUserId').value)">Delete User</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editUserModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div id="bulkActionsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Bulk Actions</h3>
                <span class="close" onclick="closeModal('bulkActionsModal')">&times;</span>
            </div>
            <div class="form-group">
                <label class="form-label">Selected Users: <span id="selectedUsersCount">0</span></label>
            </div>
            <div class="form-group">
                <label class="form-label" for="bulkAction">Action</label>
                <select id="bulkAction" class="form-select">
                    <option value="">Select Action</option>
                    <option value="block">Block Users</option>
                    <option value="unblock">Unblock Users</option>
                    <option value="delete">Soft Delete Users</option>
                    <option value="hard_delete">Permanently Delete Users</option>
                </select>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('bulkActionsModal')">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="executeBulkAction()">Execute Action</button>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div id="userDetailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">User Details</h3>
                <span class="close" onclick="closeModal('userDetailsModal')">&times;</span>
            </div>
            <div id="userDetailsContent">
                <!-- User details will be loaded here -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('userDetailsModal')">Close</button>
            </div>
        </div>
    </div>

    <!-- User Profile Modal -->
    <div id="userProfileModal" class="modal">
        <div style="width: 100%; height: 85vh; max-width: 420px; margin: 2.5vh auto; background: #f8f8f8; border: none; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.15); position: relative; overflow: hidden;">
            <span class="close" onclick="closeModal('userProfileModal')" style="position: absolute; top: 15px; right: 15px; color: white; text-shadow: 0 1px 3px rgba(0,0,0,0.5); z-index: 1000; font-size: 24px; cursor: pointer; background: rgba(0,0,0,0.4); width: 32px; height: 32px; border-radius: 16px; display: flex; align-items: center; justify-content: center;">&times;</span>
            <div id="userProfileContent" style="height: 100%; overflow-y: auto; padding: 0;">
                <!-- Profile content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- User Messages Modal -->
    <div id="userMessagesModal" class="modal">
        <div style="width: 100%; height: 85vh; max-width: 500px; margin: 2.5vh auto; background: #f5f5f5; border: none; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.15); display: flex; flex-direction: column; overflow: hidden;">
            <div style="background: #4e9af1; color: white; padding: 0.75rem 1rem; display: flex; justify-content: space-between; align-items: center; position: relative; z-index: 1000;">
                <h3 id="messagesModalTitle" style="margin: 0; font-size: 16px; font-weight: 600;">Messages</h3>
                <span class="close" onclick="closeModal('userMessagesModal')" style="color: white; font-size: 20px; cursor: pointer; background: rgba(255,255,255,0.2); width: 28px; height: 28px; border-radius: 14px; display: flex; align-items: center; justify-content: center;">&times;</span>
            </div>
            <div id="userMessagesContent" style="flex: 1; display: flex; flex-direction: column; padding: 0; height: calc(85vh - 60px);">
                <!-- Messages content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Verification Photo Modal -->
    <div id="verificationPhotoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Verification Photo</h3>
                <span class="close" onclick="closeModal('verificationPhotoModal')">&times;</span>
            </div>
            <div id="verificationPhotoContent" style="text-align: center; padding: 1rem;">
                <!-- Photo will be loaded here -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('verificationPhotoModal')">Close</button>
            </div>
        </div>
    </div>

    <!-- Verification Review Modal -->
    <div id="verificationReviewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Review Verification Request</h3>
                <span class="close" onclick="closeModal('verificationReviewModal')">&times;</span>
            </div>
            <div id="verificationReviewContent">
                <!-- Review content will be loaded here -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('verificationReviewModal')">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="rejectVerification()">Reject</button>
                <button type="button" class="btn btn-success" onclick="approveVerification()">Approve</button>
            </div>
        </div>
    </div>

    <!-- Status Change Modal -->
    <div id="statusChangeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Change Verification Status</h3>
                <span class="close" onclick="closeModal('statusChangeModal')">&times;</span>
            </div>
            <div id="statusChangeContent">
                <!-- Status change content will be loaded here -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('statusChangeModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitStatusChange()">Change Status</button>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = window.location.origin;

        // Global data storage
        let users = [];
        let matches = [];
        let messages = [];
        let reports = [];
        let reportsPagination = { page: 1, totalPages: 1, totalCount: 0 };
        let activeUsersData = [];
        let currentUser = null;
        let selectedUsers = new Set();

        // Pagination state
        let currentPage = 1;
        let totalPages = 1;
        let currentSort = { field: 'createdAt', order: 'desc' };
        let currentFilters = { search: '', role: '', accountStatus: '', premium: '' };

        // Matches pagination
        let currentMatchesPage = 1;
        let totalMatchesPages = 1;

        // Messages pagination
        let currentMessagesPage = 1;
        let totalMessagesPages = 1;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            // Create user form
            document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);

            // Edit user form
            document.getElementById('editUserForm').addEventListener('submit', handleEditUser);

            // Close modals when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            });
        }

        // Check authentication status
        async function checkAuthentication() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/auth/status`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    window.location.href = '/admin/login';
                    return;
                }

                const data = await response.json();
                currentUser = data.user;

                // Update header with user info
                document.getElementById('currentUsername').textContent = currentUser.username;
                document.getElementById('userAvatar').textContent = currentUser.username.charAt(0).toUpperCase();

                // Load dashboard data
                loadDashboardData();
                setInterval(loadDashboardData, 30000);

            } catch (error) {
                console.error('Authentication check failed:', error);
                window.location.href = '/admin/login';
            }
        }

        // Logout function
        async function logout() {
            try {
                await fetch(`${API_BASE}/api/admin/logout`, {
                    method: 'POST',
                    credentials: 'include'
                });
                window.location.href = '/admin/login';
            } catch (error) {
                console.error('Logout error:', error);
                window.location.href = '/admin/login';
            }
        }

        // Show section
        function showSection(sectionName, element) {
            // Update nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // If element is provided, use it; otherwise find the clicked element
            const targetElement = element || event.target;
            if (targetElement) {
                targetElement.classList.add('active');
            }

            // Update breadcrumb
            const breadcrumb = document.querySelector('.breadcrumb-current');
            const titles = {
                'dashboard': 'Overview',
                'analytics': 'Analytics',
                'users': 'User Management',
                'matches': 'Matches',
                'messages': 'Messages',
                'reports': 'Reports',
                'profile': 'Profile',
                'settings': 'Settings'
            };
            if (breadcrumb) {
                breadcrumb.textContent = titles[sectionName] || 'Dashboard';
            }

            // Show/hide sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });
            const section = document.getElementById(`${sectionName}-section`);
            if (section) {
                section.style.display = 'block';
            } else {
                // Default to dashboard if section doesn't exist yet
                document.getElementById('dashboard-section').style.display = 'block';
            }
        }

        // Load all dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadUsers(),
                    loadMatches(),
                    loadMessages(),
                    loadStats()
                ]);
                updateStats();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load users data with pagination and filtering
        async function loadUsers(page = 1) {
            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: 50,
                    search: currentFilters.search,
                    role: currentFilters.role,
                    accountStatus: currentFilters.accountStatus,
                    premium: currentFilters.premium,
                    sortBy: currentSort.field,
                    sortOrder: currentSort.order
                });

                const response = await fetch(`${API_BASE}/api/admin/users?${params}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                users = data.users || [];

                // Update pagination info
                if (data.pagination) {
                    currentPage = data.pagination.currentPage;
                    totalPages = data.pagination.totalPages;
                    updateUsersPagination(data.pagination);
                }

                renderUsersTable();
            } catch (error) {
                console.error('Error loading users:', error);
                showError('Failed to load users');
            }
        }

        // Load matches data
        async function loadMatches(page = 1) {
            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: 50
                });

                const response = await fetch(`${API_BASE}/api/admin/matches?${params}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                matches = data.matches || [];

                // Update pagination info
                if (data.pagination) {
                    currentMatchesPage = data.pagination?.currentPage || page;
                    totalMatchesPages = data.pagination?.totalPages || 1;
                    updateMatchesPagination(data);
                }

                renderMatchesTable();
            } catch (error) {
                console.error('Error loading matches:', error);
                showError('Failed to load matches');
            }
        }

        // Load messages data
        async function loadMessages(page = 1) {
            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: 50
                });

                const response = await fetch(`${API_BASE}/api/admin/messages?${params}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                messages = data.messages || [];

                // Update pagination info
                if (data.pagination) {
                    currentMessagesPage = data.pagination?.currentPage || page;
                    totalMessagesPages = data.pagination?.totalPages || 1;
                    updateMessagesPagination(data);
                }

                renderMessagesTable();
            } catch (error) {
                console.error('Error loading messages:', error);
                showError('Failed to load messages');
            }
        }

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/stats`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();

                // Update analytics section
                document.getElementById('analyticsActiveUsers').textContent = data.activeUsers || 0;
                document.getElementById('analyticsRecentMatches').textContent = data.recentActivity?.matches || 0;
                document.getElementById('analyticsRecentMessages').textContent = data.recentActivity?.messages || 0;
                document.getElementById('analyticsNewUsers').textContent = data.recentActivity?.newUsers || 0;

                // Update premium statistics
                if (data.premium) {
                    document.getElementById('premiumUsers').textContent = data.premium.activePremiumUsers || 0;
                    const premiumPercentage = data.totalUsers > 0 ?
                        Math.round((data.premium.activePremiumUsers / data.totalUsers) * 100) : 0;
                    document.getElementById('premiumUsersChange').textContent = `${premiumPercentage}%`;
                }

                return data;
            } catch (error) {
                console.error('Error loading stats:', error);
                return null;
            }
        }

        // Render users table
        function renderUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            if (!tbody) return;

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; color: var(--text-muted);">No users found</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => {
                const statusClass = getStatusBadgeClass(user.accountStatus || 'active');
                const roleClass = user.role === 'admin' ? 'badge-warning' : 'badge-info';
                const isPremium = user.premium && user.premium.isActive;
                const premiumClass = isPremium ? 'badge-success' : 'badge-secondary';
                const premiumText = isPremium ? 'PREMIUM' : 'REGULAR';
                const premiumIcon = isPremium ? '<svg style="width: 12px; height: 12px; margin-right: 4px;"><use href="#icon-star"/></svg>' : '';

                const isVerified = user.verification && user.verification.isVerified;
                const hasPremium = user.premium && user.premium.isActive;
                const badgeType = isVerified ? (hasPremium ? 'gold' : 'blue') : null;
                const verificationClass = isVerified ? (badgeType === 'gold' ? 'badge-premium' : 'badge-info') : 'badge-secondary';
                const verificationText = isVerified ? (badgeType === 'gold' ? 'GOLD' : 'VERIFIED') : 'NOT VERIFIED';
                const verificationIcon = isVerified ? '<svg style="width: 12px; height: 12px; margin-right: 4px;"><use href="#icon-shield-check"/></svg>' : '';

                return `
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" value="${user.id}" onchange="toggleUserSelection('${user.id}')"></td>
                        <td><strong>${user.username}</strong></td>
                        <td>${user.email || '-'}</td>
                        <td><span class="badge ${roleClass}">${(user.role || 'user').toUpperCase()}</span></td>
                        <td><span class="badge ${statusClass}">${(user.accountStatus || 'active').toUpperCase()}</span></td>
                        <td><span class="badge ${premiumClass}">${premiumIcon}${premiumText}</span></td>
                        <td><span class="badge ${verificationClass}">${verificationIcon}${verificationText}</span></td>
                        <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                        <td>${user.imageCount || 0}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-icon btn-secondary" onclick="viewUserDetails('${user.id}')" title="View Details" aria-label="View Details"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-eye"/></svg></span></button>
                                <button class="btn btn-icon btn-secondary" onclick="editUser('${user.id}')" title="Edit" aria-label="Edit"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-pencil"/></svg></span></button>
                                <button class="btn btn-icon btn-secondary" onclick="resetUserPassword('${user.id}')" title="Reset Password" aria-label="Reset Password"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-key"/></svg></span></button>
                                ${isPremium ?
                                    `<button class="btn btn-icon btn-warning" onclick="revokePremium('${user.id}')" title="Revoke Premium" aria-label="Revoke Premium"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-star"/></svg></span></button>` :
                                    `<button class="btn btn-icon btn-success" onclick="grantPremium('${user.id}')" title="Grant Premium" aria-label="Grant Premium"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-star"/></svg></span></button>`
                                }
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Render matches table
        function renderMatchesTable() {
            const tbody = document.getElementById('matchesTableBody');
            if (!tbody) return;

            if (matches.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: var(--text-muted);">No matches found</td></tr>';
                return;
            }

            tbody.innerHTML = matches.map(match => `
                <tr>
                    <td><code>${match.id.substring(0, 8)}...</code></td>
                    <td><strong>${match.user1Username}</strong></td>
                    <td><strong>${match.user2Username}</strong></td>
                    <td>${match.distance} km</td>
                    <td>${new Date(match.createdAt).toLocaleDateString()}</td>
                    <td>
                        <button class="btn btn-icon btn-secondary" onclick="viewMatchDetails('${match.id}')" title="View Details" aria-label="View Details"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-eye"/></svg></span></button>
                    </td>
                </tr>
            `).join('');
        }

        // Render messages table
        function renderMessagesTable() {
            const tbody = document.getElementById('messagesTableBody');
            if (!tbody) return;

            if (messages.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: var(--text-muted);">No messages found</td></tr>';
                return;
            }

            tbody.innerHTML = messages.map(message => `
                <tr>
                    <td><code>${message.messageId?.substring(0, 8) || 'N/A'}...</code></td>
                    <td><strong>${message.senderUsername || 'Unknown'}</strong></td>
                    <td><strong>${message.receiverUsername || 'Unknown'}</strong></td>
                    <td>${truncateText(message.text || '', 50)}</td>
                    <td>${new Date(message.timestamp).toLocaleString()}</td>
                    <td><span class="badge ${message.read ? 'badge-success' : 'badge-warning'}">${message.read ? 'READ' : 'UNREAD'}</span></td>
                </tr>
            `).join('');
        }

        // Helper function to get status badge class
        function getStatusBadgeClass(status) {
            switch (status.toLowerCase()) {
                case 'active': return 'badge-success';
                case 'blocked': return 'badge-danger';
                case 'deleted': return 'badge-warning';
                default: return 'badge-info';
            }
        }

        // Helper function to truncate text
        function truncateText(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // Pagination functions
        function updateUsersPagination(pagination) {
            const paginationContainer = document.getElementById('usersPagination');
            const usersInfo = document.getElementById('usersInfo');
            const pageInfo = document.getElementById('pageInfo');
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');

            if (paginationContainer) {
                paginationContainer.style.display = 'flex';
                usersInfo.textContent = `Showing ${users.length} of ${pagination.totalUsers} users`;
                pageInfo.textContent = `Page ${pagination.currentPage} of ${pagination.totalPages}`;

                prevBtn.disabled = !pagination.hasPrevPage;
                nextBtn.disabled = !pagination.hasNextPage;
            }
        }

        function updateMatchesPagination(data) {
            const paginationContainer = document.getElementById('matchesPagination');
            const matchesInfo = document.getElementById('matchesInfo');
            const pageInfo = document.getElementById('matchesPageInfo');
            const prevBtn = document.getElementById('prevMatchesPageBtn');
            const nextBtn = document.getElementById('nextMatchesPageBtn');

            if (paginationContainer && data.pagination) {
                paginationContainer.style.display = 'flex';
                matchesInfo.textContent = `Showing ${matches.length} of ${data.totalCount} matches`;
                pageInfo.textContent = `Page ${data.page} of ${data.totalPages}`;

                prevBtn.disabled = data.page <= 1;
                nextBtn.disabled = data.page >= data.totalPages;
            }
        }

        function updateMessagesPagination(data) {
            const paginationContainer = document.getElementById('messagesPagination');
            const messagesInfo = document.getElementById('messagesInfo');
            const pageInfo = document.getElementById('messagesPageInfo');
            const prevBtn = document.getElementById('prevMessagesPageBtn');
            const nextBtn = document.getElementById('nextMessagesPageBtn');

            if (paginationContainer && data.pagination) {
                paginationContainer.style.display = 'flex';
                messagesInfo.textContent = `Showing ${messages.length} of ${data.totalCount} messages`;
                pageInfo.textContent = `Page ${data.page} of ${data.totalPages}`;

                prevBtn.disabled = data.page <= 1;
                nextBtn.disabled = data.page >= data.totalPages;
            }
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                loadUsers(newPage);
            }
        }

        function changeMatchesPage(direction) {
            const newPage = currentMatchesPage + direction;
            if (newPage >= 1 && newPage <= totalMatchesPages) {
                loadMatches(newPage);
            }
        }

        function changeMessagesPage(direction) {
            const newPage = currentMessagesPage + direction;
            if (newPage >= 1 && newPage <= totalMessagesPages) {
                loadMessages(newPage);
            }
        }

        // Search and filter functions
        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value;
            currentFilters.search = searchTerm;
            currentPage = 1;
            loadUsers(1);
        }

        function filterUsers() {
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const premiumFilter = document.getElementById('premiumFilter').value;

            currentFilters.role = roleFilter;
            currentFilters.accountStatus = statusFilter;
            currentFilters.premium = premiumFilter;
            currentPage = 1;
            loadUsers(1);
        }

        function sortUsers(field) {
            if (currentSort.field === field) {
                currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.order = 'asc';
            }
            currentPage = 1;
            loadUsers(1);
        }

        function searchMatches() {
            console.log('Match search not implemented yet');
        }

        function searchMessages() {
            console.log('Message search not implemented yet');
        }

        function refreshMatches() {
            loadMatches(currentMatchesPage);
        }

        function refreshMessages() {
            loadMessages(currentMessagesPage);
        }

        // User selection functions
        function toggleUserSelection(userId) {
            const checkbox = document.querySelector(`input[value="${userId}"]`);
            if (checkbox.checked) {
                selectedUsers.add(userId);
            } else {
                selectedUsers.delete(userId);
            }
            updateBulkActionsButton();
        }

        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllUsers');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');

            userCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                if (selectAllCheckbox.checked) {
                    selectedUsers.add(checkbox.value);
                } else {
                    selectedUsers.delete(checkbox.value);
                }
            });
            updateBulkActionsButton();
        }

        function updateBulkActionsButton() {
            const bulkActionsBtn = document.getElementById('bulkActionsBtn');
            if (selectedUsers.size > 0) {
                bulkActionsBtn.style.display = 'inline-block';
            } else {
                bulkActionsBtn.style.display = 'none';
            }
        }

        // Modal functions
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function showCreateUserModal() {
            document.getElementById('createUserForm').reset();
            showModal('createUserModal');
        }

        function showBulkActionsModal() {
            document.getElementById('selectedUsersCount').textContent = selectedUsers.size;
            showModal('bulkActionsModal');
        }

        // Update statistics
        function updateStats() {
            const totalUsersEl = document.getElementById('totalUsers');
            const activeUsersEl = document.getElementById('activeUsers');
            const totalMatchesEl = document.getElementById('totalMatches');
            const premiumUsersEl = document.getElementById('premiumUsers');

            if (totalUsersEl) totalUsersEl.textContent = users.length > 0 ? `${users.length}` : '0';
            if (activeUsersEl) activeUsersEl.textContent = '0';
            if (totalMatchesEl) totalMatchesEl.textContent = matches.length > 0 ? `${matches.length}` : '0';
            if (premiumUsersEl) premiumUsersEl.textContent = '0'; // Will be updated by loadStats
        }

        // Error handling
        function showError(message) {
            console.error(message);
            alert(message);
        }

        function showSuccess(message) {
            console.log(message);
            alert(message);
        }

        // User management functions
        async function handleCreateUser(event) {
            event.preventDefault();

            const formData = {
                username: document.getElementById('newUsername').value,
                email: document.getElementById('newEmail').value || undefined,
                password: document.getElementById('newPassword').value,
                role: document.getElementById('newRole').value,
                age: parseInt(document.getElementById('newAge').value) || undefined,
                description: document.getElementById('newDescription').value || ''
            };

            try {
                const response = await fetch(`${API_BASE}/api/admin/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to create user');
                }

                const result = await response.json();
                showSuccess('User created successfully');
                closeModal('createUserModal');
                loadUsers(currentPage);
            } catch (error) {
                console.error('Error creating user:', error);
                showError(error.message);
            }
        }

        async function editUser(userId) {
            try {
                const user = users.find(u => u.id === userId);
                if (!user) {
                    showError('User not found');
                    return;
                }

                document.getElementById('editUserId').value = userId;
                document.getElementById('editUsername').value = user.username;
                document.getElementById('editEmail').value = user.email || '';
                document.getElementById('editRole').value = user.role || 'user';
                document.getElementById('editAccountStatus').value = user.accountStatus || 'active';
                document.getElementById('editAge').value = user.age || '';
                document.getElementById('editDescription').value = user.description || '';

                showModal('editUserModal');
            } catch (error) {
                console.error('Error loading user for edit:', error);
                showError('Failed to load user data');
            }
        }

        async function handleEditUser(event) {
            event.preventDefault();

            const userId = document.getElementById('editUserId').value;
            const formData = {
                username: document.getElementById('editUsername').value,
                email: document.getElementById('editEmail').value || undefined,
                role: document.getElementById('editRole').value,
                accountStatus: document.getElementById('editAccountStatus').value,
                age: parseInt(document.getElementById('editAge').value) || undefined,
                description: document.getElementById('editDescription').value || ''
            };

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to update user');
                }

                const result = await response.json();
                showSuccess('User updated successfully');
                closeModal('editUserModal');
                loadUsers(currentPage);
            } catch (error) {
                console.error('Error updating user:', error);
                showError(error.message);
            }
        }

        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || 'Failed to delete user');
                }

                showSuccess('User deleted successfully');
                closeModal('editUserModal');
                loadUsers(currentPage);
            } catch (error) {
                console.error('Error deleting user:', error);
                showError(error.message);
            }
        }

        async function resetUserPassword(userId) {
            const newPassword = prompt('Enter new password for user:');
            if (!newPassword) return;

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}/password`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ password: newPassword })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to reset password');
                }
            } catch (error) {
                console.error('Error resetting password:', error);
                showError(error.message);
            }
        }

        // Premium management functions
        async function grantPremium(userId) {
            const subscriptionType = prompt('Enter subscription type (monthly/yearly):');
            if (!subscriptionType || !['monthly', 'yearly'].includes(subscriptionType.toLowerCase())) {
                showError('Please enter "monthly" or "yearly"');
                return;
            }

            const durationMonths = subscriptionType.toLowerCase() === 'monthly' ? 1 : 12;

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}/premium`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        subscriptionType: subscriptionType.toLowerCase(),
                        durationMonths
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to grant premium');
                }

                const result = await response.json();
                showSuccess(`Premium subscription granted to ${result.user.username}`);
                loadUsers(currentPage);
                loadStats(); // Refresh premium statistics
            } catch (error) {
                console.error('Error granting premium:', error);
                showError(error.message);
            }
        }

        async function revokePremium(userId) {
            if (!confirm('Are you sure you want to revoke premium subscription from this user?')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}/premium`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to revoke premium');
                }

                const result = await response.json();
                showSuccess(`Premium subscription revoked from ${result.user.username}`);
                loadUsers(currentPage);
                loadStats(); // Refresh premium statistics
            } catch (error) {
                console.error('Error revoking premium:', error);
                showError(error.message);
            }
        }

        // Reports: fetch and render
        async function loadReports(page = 1) {
            try {
                const status = document.getElementById('reportsStatusFilter')?.value || 'open';
                const params = new URLSearchParams({ page, limit: 20, status });
                const url = `${API_BASE}/api/admin/reports?${params}`;
                console.log('Loading reports from:', url);
                const response = await fetch(url, { credentials: 'include' });
                console.log('Reports response status:', response.status);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                const data = await response.json();
                console.log('Reports data:', data);
                reports = data.reports || [];
                reportsPagination = { page: data.page, totalPages: data.totalPages, totalCount: data.totalCount };
                renderReportsTable();
            } catch (e) {
                console.error('Failed to load reports', e);
                const tbody = document.getElementById('reportsTableBody');
                if (tbody) {
                    tbody.innerHTML = `<tr><td colspan="6" style="text-align:center;color:red">Error loading reports: ${e.message}</td></tr>`;
                }
            }
        }

        function changeReportsPage(delta) {
            const next = Math.min(Math.max(1, (reportsPagination.page || 1) + delta), reportsPagination.totalPages || 1);
            if (next !== reportsPagination.page) loadReports(next);
        }

        function renderReportsTable() {
            const tbody = document.getElementById('reportsTableBody');
            const pageInfo = document.getElementById('reportsPageInfo');
            if (!tbody) return;
            if (!reports || reports.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align:center;color:var(--text-muted)">No reports</td></tr>';
                pageInfo.textContent = `Page ${reportsPagination.page || 1} of ${reportsPagination.totalPages || 1}`;
                return;
            }
            tbody.innerHTML = reports.map(r => {
                const reported = r.reportedUserId || {};
                const reporter = r.reporterId || {};
                const statusBadge = r.status === 'open' ? 'badge-warning' : 'badge-success';
                const statusText = r.status.charAt(0).toUpperCase() + r.status.slice(1);
                const date = new Date(r.createdAt).toLocaleString();
                return `
                    <tr>
                        <td>${reported.username || 'N/A'}</td>
                        <td>${reporter.username || 'N/A'}</td>
                        <td title="${escapeHtml(r.reason)}">${escapeHtml(r.reason)}</td>
                        <td>${date}</td>
                        <td><span class="badge ${statusBadge}">${statusText}</span></td>
                        <td>
                            <div style="display: flex; gap: 0.25rem; flex-wrap: wrap;">
                                <button class="btn btn-small btn-info" onclick="viewUserProfile('${reported._id || reported}', '${reported.username || 'N/A'}')">Profile</button>
                                <button class="btn btn-small btn-info" onclick="viewUserMessages('${reporter._id || reporter}', '${reported._id || reported}', '${reporter.username || 'N/A'}', '${reported.username || 'N/A'}')">Messages</button>
                                ${r.status === 'open' ? `
                                <button class="btn btn-small" onclick="resolveReport('${r._id}', 'warning')">Warn</button>
                                <button class="btn btn-small btn-danger" onclick="resolveReport('${r._id}', 'block')">Block</button>
                                <button class="btn btn-small btn-secondary" onclick="resolveReport('${r._id}', 'ignore')">Ignore</button>
                                ` : `<span class="text-muted">Resolved</span>`}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
            pageInfo.textContent = `Page ${reportsPagination.page || 1} of ${reportsPagination.totalPages || 1}`;
        }

        async function resolveReport(reportId, action) {
            const notes = prompt(`Add notes for ${action} (optional):`, '');
            try {
                const response = await fetch(`${API_BASE}/api/admin/reports/${reportId}/resolve`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ action, notes })
                });
                if (!response.ok) {
                    const err = await response.json().catch(() => ({}));
                    throw new Error(err.error || `HTTP ${response.status}`);
                }
                await loadReports(reportsPagination.page || 1);
                // Refresh users so warningCount/accountStatus are updated in the users view
                await loadUsers(1);
            } catch (e) {
                alert('Failed to resolve report: ' + e.message);
            }
        }

        function escapeHtml(str = '') {
            return String(str)
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');
        }

        // Hook section switching to load reports and verification when opened
        const originalShowSection = showSection;
        showSection = function(sectionName) {
            originalShowSection(sectionName);
            if (sectionName === 'reports') {
                console.log('Loading reports section...');
                loadReports(1);
            } else if (sectionName === 'verification') {
                console.log('Loading verification section...');
                loadVerificationRequests(1);
            }
        }

        // Add event listeners for status filters
        document.addEventListener('DOMContentLoaded', function() {
            const reportsStatusFilter = document.getElementById('reportsStatusFilter');
            if (reportsStatusFilter) {
                reportsStatusFilter.addEventListener('change', function() {
                    loadReports(1);
                });
            }

            const verificationStatusFilter = document.getElementById('verificationStatusFilter');
            if (verificationStatusFilter) {
                verificationStatusFilter.addEventListener('change', function() {
                    loadVerificationRequests(1);
                });
            }
        });

        // ===== VERIFICATION FUNCTIONS =====

        let verificationRequests = [];
        let verificationPagination = { page: 1, totalPages: 1, totalCount: 0 };
        let currentVerificationRequest = null;

        // Load verification requests
        async function loadVerificationRequests(page = 1) {
            try {
                const statusFilter = document.getElementById('verificationStatusFilter')?.value || 'pending';
                const params = new URLSearchParams({
                    page: page.toString(),
                    limit: '20',
                    status: statusFilter === 'all' ? '' : statusFilter,
                    sortBy: 'submittedAt',
                    sortOrder: 'desc'
                });

                const response = await fetch(`${API_BASE}/api/admin/verification/requests?${params}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                verificationRequests = data.requests || [];
                verificationPagination = data.pagination || { page: 1, totalPages: 1, totalCount: 0 };

                renderVerificationRequests();
                updateVerificationPagination();

                // Load stats
                await loadVerificationStats();

            } catch (error) {
                console.error('Error loading verification requests:', error);
                document.getElementById('verificationTableBody').innerHTML =
                    '<tr><td colspan="5" style="text-align:center;color:var(--text-danger)">Failed to load verification requests</td></tr>';
            }
        }

        // Load verification statistics
        async function loadVerificationStats() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/verification/stats`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();

                document.getElementById('verificationPendingCount').textContent = data.requests.pending || 0;
                document.getElementById('verificationVerifiedCount').textContent = data.verifiedUsers.total || 0;
                document.getElementById('verificationBlueCount').textContent = data.verifiedUsers.withoutPremium || 0;
                document.getElementById('verificationGoldCount').textContent = data.verifiedUsers.withPremium || 0;

            } catch (error) {
                console.error('Error loading verification stats:', error);
            }
        }

        // Render verification requests table
        function renderVerificationRequests() {
            const tbody = document.getElementById('verificationTableBody');

            if (!verificationRequests || verificationRequests.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align:center;color:var(--text-muted)">No verification requests found</td></tr>';
                return;
            }

            tbody.innerHTML = verificationRequests.map(request => {
                const statusClass = request.status === 'pending' ? 'warning' :
                                  request.status === 'approved' ? 'success' : 'danger';

                const submittedDate = new Date(request.submittedAt).toLocaleDateString();
                const reviewedBy = request.reviewedBy ? request.reviewedBy.username : '-';

                return `
                    <tr>
                        <td>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <strong>${escapeHtml(request.username)}</strong>
                                ${request.userId?.verification?.isVerified ? '<span style="color: var(--color-success);">✓</span>' : ''}
                            </div>
                        </td>
                        <td>${submittedDate}</td>
                        <td><span class="status-badge ${statusClass}">${request.status}</span></td>
                        <td>${escapeHtml(reviewedBy)}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-xs btn-secondary" onclick="viewVerificationPhoto('${request._id}')" title="View Photo">
                                    <svg aria-hidden="true" focusable="false" style="width: 14px; height: 14px;"><use href="#icon-eye"/></svg>
                                </button>
                                ${request.status === 'pending' ? `
                                    <button class="btn btn-sm btn-primary" onclick="reviewVerificationRequest('${request._id}')" title="Review">
                                        Review
                                    </button>
                                ` : `
                                    <button class="btn btn-xs btn-warning" onclick="changeVerificationStatus('${request._id}')" title="Change Status">
                                        Change
                                    </button>
                                `}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Update verification pagination
        function updateVerificationPagination() {
            const pageInfo = document.getElementById('verificationPageInfo');
            const pagination = document.getElementById('verificationPagination');

            if (verificationPagination.totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }

            pagination.style.display = 'flex';
            pageInfo.textContent = `Page ${verificationPagination.currentPage} of ${verificationPagination.totalPages}`;

            const prevBtn = pagination.querySelector('button:first-child');
            const nextBtn = pagination.querySelector('button:last-child');

            prevBtn.disabled = verificationPagination.currentPage <= 1;
            nextBtn.disabled = verificationPagination.currentPage >= verificationPagination.totalPages;
        }

        // Change verification page
        function changeVerificationPage(direction) {
            const newPage = verificationPagination.currentPage + direction;
            if (newPage >= 1 && newPage <= verificationPagination.totalPages) {
                loadVerificationRequests(newPage);
            }
        }

        // View verification photo
        async function viewVerificationPhoto(requestId) {
            try {
                const request = verificationRequests.find(r => r._id === requestId);
                if (!request) {
                    alert('Verification request not found');
                    return;
                }

                const photoContent = document.getElementById('verificationPhotoContent');
                photoContent.innerHTML = `
                    <div style="margin-bottom: 1rem;">
                        <h4>Verification Photo for ${escapeHtml(request.username)}</h4>
                        <p style="color: var(--text-muted); margin: 0.5rem 0;">Submitted: ${new Date(request.submittedAt).toLocaleString()}</p>
                    </div>
                    <div style="max-width: 100%; max-height: 500px; overflow: hidden; border-radius: 8px; border: 1px solid var(--border-color);">
                        <img src="${request.verificationPhoto}" alt="Verification Photo" style="width: 100%; height: auto; display: block;">
                    </div>
                `;

                showModal('verificationPhotoModal');

            } catch (error) {
                console.error('Error viewing verification photo:', error);
                alert('Failed to load verification photo');
            }
        }

        // Review verification request
        async function reviewVerificationRequest(requestId) {
            try {
                const response = await fetch(`${API_BASE}/api/admin/verification/requests/${requestId}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                currentVerificationRequest = data.request;

                const reviewContent = document.getElementById('verificationReviewContent');
                reviewContent.innerHTML = `
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                        <div>
                            <h4>User Information</h4>
                            <div style="background: var(--bg-secondary); padding: 1rem; border-radius: 8px; margin-top: 0.5rem;">
                                <p><strong>Username:</strong> ${escapeHtml(currentVerificationRequest.username)}</p>
                                <p><strong>Email:</strong> ${escapeHtml(currentVerificationRequest.userId?.email || 'Not provided')}</p>
                                <p><strong>Account Created:</strong> ${new Date(currentVerificationRequest.userId?.createdAt).toLocaleDateString()}</p>
                                <p><strong>Current Status:</strong> ${currentVerificationRequest.userId?.verification?.isVerified ? 'Verified' : 'Not Verified'}</p>
                                <p><strong>Request Submitted:</strong> ${new Date(currentVerificationRequest.submittedAt).toLocaleString()}</p>
                                ${currentVerificationRequest.requiredFingers ? `<p><strong>Required Fingers:</strong> ${currentVerificationRequest.requiredFingers}</p>` : '<p><strong>Required Fingers:</strong> <em>Not specified (older request)</em></p>'}
                            </div>
                        </div>
                        <div>
                            <h4>Verification Photo</h4>
                            <div style="margin-top: 0.5rem; border-radius: 8px; overflow: hidden; border: 1px solid var(--border-color);">
                                <img src="${currentVerificationRequest.verificationPhoto}" alt="Verification Photo" style="width: 100%; height: auto; display: block; max-height: 300px; object-fit: cover;">
                            </div>
                        </div>
                    </div>

                    <div id="rejectionReasonDiv" style="margin-bottom: 1rem;">
                        <label for="rejectionReason" style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Rejection Reason</label>
                        <select id="rejectionReason" style="width: 100%; padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 4px; background: var(--bg-primary); color: var(--text-primary);">
                            <option value="photo_unclear">Photo is unclear or low quality</option>
                            <option value="photo_inappropriate">Photo is inappropriate</option>
                            <option value="identity_mismatch">Identity doesn't match profile</option>
                            <option value="fake_document">Fake or altered document</option>
                            <option value="other">Other reason</option>
                        </select>
                    </div>
                `;

                showModal('verificationReviewModal');

            } catch (error) {
                console.error('Error loading verification request:', error);
                alert('Failed to load verification request details');
            }
        }

        // Approve verification
        async function approveVerification() {
            if (!currentVerificationRequest) return;

            try {
                const response = await fetch(`${API_BASE}/api/admin/verification/requests/${currentVerificationRequest._id}/approve`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({})
                });

                if (!response.ok) {
                    const error = await response.json().catch(() => ({}));
                    throw new Error(error.error || `HTTP ${response.status}`);
                }

                closeModal('verificationReviewModal');
                await loadVerificationRequests(verificationPagination.currentPage);
                alert('Verification request approved successfully!');

            } catch (error) {
                console.error('Error approving verification:', error);
                alert('Failed to approve verification: ' + error.message);
            }
        }

        // Reject verification
        async function rejectVerification() {
            if (!currentVerificationRequest) return;

            const reason = document.getElementById('rejectionReason').value;

            if (!reason) {
                alert('Please select a rejection reason.');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/verification/requests/${currentVerificationRequest._id}/reject`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ reason })
                });

                if (!response.ok) {
                    const error = await response.json().catch(() => ({}));
                    throw new Error(error.error || `HTTP ${response.status}`);
                }

                closeModal('verificationReviewModal');
                await loadVerificationRequests(verificationPagination.currentPage);
                alert('Verification request rejected.');

            } catch (error) {
                console.error('Error rejecting verification:', error);
                alert('Failed to reject verification: ' + error.message);
            }
        }

        // Change verification status
        async function changeVerificationStatus(requestId) {
            try {
                const request = verificationRequests.find(r => r._id === requestId);
                if (!request) {
                    alert('Verification request not found');
                    return;
                }

                const statusChangeContent = document.getElementById('statusChangeContent');
                statusChangeContent.innerHTML = `
                    <div style="margin-bottom: 1rem;">
                        <h4>Request Details</h4>
                        <p><strong>User:</strong> ${escapeHtml(request.username)}</p>
                        <p><strong>Current Status:</strong> <span class="status-badge ${request.status}">${request.status}</span></p>
                        <p><strong>Submitted:</strong> ${new Date(request.submittedAt).toLocaleString()}</p>
                        ${request.reviewedAt ? `<p><strong>Last Reviewed:</strong> ${new Date(request.reviewedAt).toLocaleString()}</p>` : ''}
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <label for="newStatus" style="display: block; margin-bottom: 0.5rem; font-weight: 600;">New Status</label>
                        <select id="newStatus" style="width: 100%; padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 4px; background: var(--bg-primary); color: var(--text-primary);">
                            <option value="pending" ${request.status === 'pending' ? 'selected' : ''}>Pending</option>
                            <option value="approved" ${request.status === 'approved' ? 'selected' : ''}>Approved</option>
                            <option value="rejected" ${request.status === 'rejected' ? 'selected' : ''}>Rejected</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <label for="statusChangeNotes" style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Notes (Optional)</label>
                        <textarea id="statusChangeNotes" placeholder="Add any notes about this status change..." style="width: 100%; min-height: 80px; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 4px; background: var(--bg-primary); color: var(--text-primary);"></textarea>
                    </div>
                `;

                // Store the request ID for submission
                statusChangeContent.dataset.requestId = requestId;

                showModal('statusChangeModal');

            } catch (error) {
                console.error('Error preparing status change:', error);
                alert('Failed to prepare status change');
            }
        }

        // Submit status change
        async function submitStatusChange() {
            try {
                const statusChangeContent = document.getElementById('statusChangeContent');
                const requestId = statusChangeContent.dataset.requestId;
                const newStatus = document.getElementById('newStatus').value;
                const notes = document.getElementById('statusChangeNotes').value;

                if (!requestId || !newStatus) {
                    alert('Missing required information');
                    return;
                }

                const response = await fetch(`${API_BASE}/api/admin/verification/requests/${requestId}/status`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ status: newStatus, notes })
                });

                if (!response.ok) {
                    const error = await response.json().catch(() => ({}));
                    throw new Error(error.error || `HTTP ${response.status}`);
                }

                closeModal('statusChangeModal');
                await loadVerificationRequests(verificationPagination.currentPage);
                alert('Verification status changed successfully!');

            } catch (error) {
                console.error('Error changing status:', error);
                alert('Failed to change status: ' + error.message);
            }
        }

        // View user profile in modal
        async function viewUserProfile(userId, username) {
            try {
                // Show loading state
                document.getElementById('userProfileContent').innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8f8f8;">
                        <div style="text-align: center;">
                            <div class="loading-spinner"></div>
                            <p style="margin-top: 1rem; color: #666;">Loading ${username}'s profile...</p>
                        </div>
                    </div>
                `;
                showModal('userProfileModal');

                // Fetch user profile
                const response = await fetch(`${API_BASE}/api/profile/${userId}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                const profile = data.profile;

                // Set global variables for image navigation
                profileImages = profile.images || [];
                currentProfileImageIndex = 0;

                // Create profile HTML exactly like the app
                const profileHtml = `
                    <div class="app-profile-view" style="background: #f8f8f8; margin: 0; padding: 0; border-radius: 12px 12px 0 0; height: 100%; overflow-y: auto;">
                        <!-- Photo Section -->
                        <div class="app-photo-section" style="position: relative; width: 100%; height: 300px; background: #eee; border-radius: 12px 12px 0 0;">
                            ${profile.images && profile.images.length > 0 ? `
                                <img id="profileImage" src="${profile.images[0].startsWith('data:') ? profile.images[0] : `data:image/jpeg;base64,${profile.images[0]}`}"
                                     style="width: 100%; height: 100%; object-fit: cover;" alt="Profile photo">

                                ${profile.images.length > 1 ? `
                                    <!-- Image navigation buttons -->
                                    <button onclick="prevProfileImage()" style="position: absolute; top: 50%; left: 10px; width: 50px; height: 50px; border-radius: 25px; background: rgba(0, 0, 0, 0.3); border: none; color: white; font-size: 28px; cursor: pointer; transform: translateY(-25px); display: flex; align-items: center; justify-content: center;">
                                        ‹
                                    </button>
                                    <button onclick="nextProfileImage()" style="position: absolute; top: 50%; right: 10px; width: 50px; height: 50px; border-radius: 25px; background: rgba(0, 0, 0, 0.3); border: none; color: white; font-size: 28px; cursor: pointer; transform: translateY(-25px); display: flex; align-items: center; justify-content: center;">
                                        ›
                                    </button>

                                    <!-- Image dots -->
                                    <div style="position: absolute; bottom: 20px; left: 0; right: 0; display: flex; justify-content: center; align-items: center;">
                                        ${profile.images.map((_, index) => `
                                            <div class="image-dot" data-index="${index}" style="width: ${index === 0 ? '10px' : '8px'}; height: ${index === 0 ? '10px' : '8px'}; border-radius: 50%; background: ${index === 0 ? '#fff' : 'rgba(255, 255, 255, 0.5)'}; margin: 0 4px; cursor: pointer;" onclick="goToProfileImage(${index})"></div>
                                        `).join('')}
                                    </div>
                                ` : ''}
                            ` : `
                                <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: #eee;">
                                    <div style="width: 150px; height: 150px; border-radius: 75px; background: #4e9af1; display: flex; align-items: center; justify-content: center; color: white; font-size: 80px; font-weight: bold;">
                                        ${username.charAt(0).toUpperCase()}
                                    </div>
                                </div>
                            `}
                        </div>

                        <!-- Info Section -->
                        <div class="app-info-section" style="padding: 20px; background: #fff; border-top-left-radius: 20px; border-top-right-radius: 20px; margin-top: -20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <h2 style="margin: 0; font-size: 24px; font-weight: bold; color: #333;">
                                        ${username}${profile.age ? `, ${profile.age}` : ''}
                                    </h2>
                                    ${profile.verification?.isVerified ? `
                                        <div class="verification-badge ${profile.badgeType || 'blue'}" style="display: flex; align-items: center; gap: 4px;">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M12 2L3 7v5c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-9-5zm-2 13l-4-4 1.41-1.41L10 12.17l6.59-6.59L18 7l-8 8z"/>
                                            </svg>
                                            <span>Verified</span>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>

                            ${!profile || Object.keys(profile).length === 0 ? `
                                <div style="background: #fff8e6; padding: 15px; border-radius: 10px; margin-bottom: 20px; display: flex; align-items: center; border: 1px solid #ffcc66;">
                                    <span style="color: #ff9500; margin-right: 10px;">⚠️</span>
                                    <span style="color: #ff9500; font-size: 14px;">This user hasn't completed their profile yet</span>
                                </div>
                            ` : ''}

                            <!-- Description -->
                            ${profile.description ? `
                                <div style="margin-bottom: 20px;">
                                    <p style="font-size: 16px; line-height: 24px; color: #333; margin: 0;">${escapeHtml(profile.description)}</p>
                                </div>
                            ` : `
                                <div style="margin-bottom: 20px; padding: 20px; background: #f0f0f0; border-radius: 10px; text-align: center;">
                                    <p style="color: #999; font-size: 16px; margin: 0;">No description available</p>
                                </div>
                            `}

                            <!-- Passions/Interests -->
                            ${profile.passions && profile.passions.length > 0 ? `
                                <div style="margin-top: 10px; margin-bottom: 20px;">
                                    <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 10px; color: #333;">Interests</h3>
                                    <div style="display: flex; flex-wrap: wrap;">
                                        ${profile.passions.map(passion => `
                                            <div style="background: #e6f0ff; color: #4e9af1; padding: 8px 15px; border-radius: 20px; margin-right: 10px; margin-bottom: 10px; border: 1px solid #4e9af1; font-size: 14px; font-weight: 500;">
                                                ${escapeHtml(passion)}
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;

                document.getElementById('userProfileContent').innerHTML = profileHtml;

            } catch (error) {
                console.error('Error loading user profile:', error);
                document.getElementById('userProfileContent').innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #ff3b30;">
                        <p>Error loading profile: ${error.message}</p>
                        <button class="btn btn-primary" onclick="viewUserProfile('${userId}', '${username}')">Retry</button>
                    </div>
                `;
            }
        }

        // Profile image navigation functions
        let currentProfileImageIndex = 0;
        let profileImages = [];

        function nextProfileImage() {
            if (profileImages.length > 1) {
                currentProfileImageIndex = (currentProfileImageIndex + 1) % profileImages.length;
                updateProfileImage();
            }
        }

        function prevProfileImage() {
            if (profileImages.length > 1) {
                currentProfileImageIndex = currentProfileImageIndex === 0 ? profileImages.length - 1 : currentProfileImageIndex - 1;
                updateProfileImage();
            }
        }

        function goToProfileImage(index) {
            currentProfileImageIndex = index;
            updateProfileImage();
        }

        function updateProfileImage() {
            const img = document.getElementById('profileImage');
            const dots = document.querySelectorAll('.image-dot');

            if (img && profileImages[currentProfileImageIndex]) {
                const imageSrc = profileImages[currentProfileImageIndex].startsWith('data:')
                    ? profileImages[currentProfileImageIndex]
                    : `data:image/jpeg;base64,${profileImages[currentProfileImageIndex]}`;
                img.src = imageSrc;
            }

            // Update dots
            dots.forEach((dot, index) => {
                if (index === currentProfileImageIndex) {
                    dot.style.width = '10px';
                    dot.style.height = '10px';
                    dot.style.background = '#fff';
                } else {
                    dot.style.width = '8px';
                    dot.style.height = '8px';
                    dot.style.background = 'rgba(255, 255, 255, 0.5)';
                }
            });
        }

        // View messages between users in modal
        async function viewUserMessages(userId1, userId2, username1, username2) {
            try {
                document.getElementById('messagesModalTitle').textContent = `Messages: ${username1} ↔ ${username2}`;

                // Show loading state
                document.getElementById('userMessagesContent').innerHTML = `
                    <div style="flex: 1; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center;">
                            <div class="loading-spinner"></div>
                            <p>Loading messages...</p>
                        </div>
                    </div>
                `;
                showModal('userMessagesModal');

                // Fetch messages between users
                const response = await fetch(`${API_BASE}/api/admin/messages/${userId1}/${userId2}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                const messages = data.messages || [];

                // Create messages HTML exactly like the app's chat screen
                const messagesHtml = `
                    <div style="flex: 1; overflow-y: auto; background: #f5f5f5; padding: 1rem 0;">
                        ${messages.length > 0 ? messages.map(msg => {
                            const isUser1 = msg.senderId === userId1;
                            const justifyContent = isUser1 ? 'justify-content: flex-end' : 'justify-content: flex-start';
                            const bubbleStyle = isUser1 ? 'background: #4e9af1; color: white;' : 'background: white; color: #333;';
                            return `
                                <div style="display: flex; ${justifyContent}; margin-bottom: 1rem; padding: 0 1rem;">
                                    <div style="max-width: 70%; ${bubbleStyle} padding: 12px 16px; border-radius: 18px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                        <div style="font-size: 16px; line-height: 1.4; margin-bottom: 4px;">
                                            ${escapeHtml(msg.text)}
                                        </div>
                                        <div style="font-size: 11px; opacity: 0.7; text-align: right;">
                                            ${new Date(msg.timestamp).toLocaleString()}
                                        </div>
                                    </div>
                                </div>
                            `;
                        }).join('') : `
                            <div style="flex: 1; display: flex; align-items: center; justify-content: center;">
                                <div style="text-align: center; color: #666;">
                                    <p style="font-size: 16px; margin: 0;">No messages found between these users.</p>
                                </div>
                            </div>
                        `}
                    </div>
                    <div style="padding: 1rem; background: white; border-top: 1px solid #eee;">
                        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 14px; color: #666;">
                            <span>Total messages: ${messages.length}</span>
                            <span>${username1} ↔ ${username2}</span>
                        </div>
                    </div>
                `;

                document.getElementById('userMessagesContent').innerHTML = messagesHtml;

            } catch (error) {
                console.error('Error loading messages:', error);
                document.getElementById('userMessagesContent').innerHTML = `
                    <div style="flex: 1; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center; color: #ff3b30;">
                            <p>Error loading messages: ${error.message}</p>
                            <button class="btn btn-primary" onclick="viewUserMessages('${userId1}', '${userId2}', '${username1}', '${username2}')">Retry</button>
                        </div>
                    </div>
                `;
            }
        }

        // Test function to debug reports API (global scope)
        async function testReportsAPI() {
            console.log('=== TESTING REPORTS API ===');
            try {
                // Test 1: Check if we're authenticated
                console.log('Testing authentication...');
                const authTest = await fetch(`${API_BASE}/api/admin/users?page=1&limit=1`, { credentials: 'include' });
                console.log('Auth test status:', authTest.status);

                // Test 2: Try to fetch reports with different parameters
                console.log('Testing reports API...');
                const tests = [
                    { status: 'all', page: 1, limit: 50 },
                    { status: 'open', page: 1, limit: 50 },
                    { status: 'resolved', page: 1, limit: 50 }
                ];

                for (const test of tests) {
                    const params = new URLSearchParams(test);
                    const url = `${API_BASE}/api/admin/reports?${params}`;
                    console.log(`Testing: ${url}`);

                    const response = await fetch(url, { credentials: 'include' });
                    console.log(`Status: ${response.status}`);

                    if (response.ok) {
                        const data = await response.json();
                        console.log(`Data:`, data);
                    } else {
                        const errorText = await response.text();
                        console.log(`Error:`, errorText);
                    }
                }
            } catch (error) {
                console.error('Test failed:', error);
            }
        }



        async function executeBulkAction() {
            const action = document.getElementById('bulkAction').value;
            if (!action) {
                showError('Please select an action');
                return;
            }

            const userIds = Array.from(selectedUsers);
            if (userIds.length === 0) {
                showError('No users selected');
                return;
            }

            const confirmMessage = `Are you sure you want to ${action} ${userIds.length} user(s)?`;
            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/bulk`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ action, userIds })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Bulk operation failed');
                }

                const result = await response.json();
                showSuccess(`Bulk ${action} completed: ${result.affected} users affected`);
                closeModal('bulkActionsModal');
                selectedUsers.clear();
                updateBulkActionsButton();
                loadUsers(currentPage);
            } catch (error) {
                console.error('Error in bulk operation:', error);
                showError(error.message);
            }
        }

        async function viewUserDetails(userId) {
            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}/activity`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error('Failed to load user details');
                }

                const data = await response.json();
                const user = data.user;
                const stats = data.stats;
                const recentMatches = data.recentMatches;

                const detailsHtml = `
                    <div class="user-details">
                        <h4>User Information</h4>
                        <p><strong>Username:</strong> ${user.username}</p>
                        <p><strong>Email:</strong> ${user.email || 'Not provided'}</p>
                        <p><strong>Role:</strong> ${user.role || 'user'}</p>
                        <p><strong>Status:</strong> ${user.accountStatus || 'active'}</p>
                        <p><strong>Age:</strong> ${user.age || 'Not provided'}</p>
                        <p><strong>Description:</strong> ${user.description || 'No description'}</p>
                        <p><strong>Created:</strong> ${new Date(user.createdAt).toLocaleString()}</p>

                        <h4>Statistics</h4>
                        <p><strong>Total Matches:</strong> ${stats.totalMatches}</p>
                        <p><strong>Total Messages:</strong> ${stats.totalMessages}</p>
                        <p><strong>Sent Messages:</strong> ${stats.sentMessages}</p>
                        <p><strong>Received Messages:</strong> ${stats.receivedMessages}</p>
                        <p><strong>Account Age:</strong> ${stats.accountAge} days</p>

                        <h4>Recent Matches</h4>
                        ${recentMatches.length > 0 ?
                            recentMatches.map(match =>
                                `<p>• ${match.partner} (${new Date(match.createdAt).toLocaleDateString()})</p>`
                            ).join('') :
                            '<p>No recent matches</p>'
                        }
                    </div>
                `;

                document.getElementById('userDetailsContent').innerHTML = detailsHtml;
                showModal('userDetailsModal');
            } catch (error) {
                console.error('Error loading user details:', error);
                showError('Failed to load user details');
            }
        }

        function viewMatchDetails(matchId) {
            console.log('Match details for:', matchId);
            showError('Match details view not implemented yet');
        }
    </script>
</body>
</html>