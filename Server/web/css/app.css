/* Desktop Shake & Match App Styles */
:root {
    --primary-color: #4e9af1;
    --primary-dark: #3a7bc8;
    --secondary-color: #f8f9fa;
    --accent-color: #ff6b6b;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #bdc3c7;
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --border-color: #dee2e6;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 24px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    color: var(--text-primary);
    overflow: hidden;
}

.app-container {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* Loading Screen */
.loading-screen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1000 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.loading-content {
    text-align: center;
    color: white;
}

.logo-container {
    margin-bottom: 2rem;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
}

.logo-icon::before {
    content: '💕';
    font-size: 2rem;
}

.loading-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 2rem auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    pointer-events: none;
}

.screen.active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Login Screen */
.login-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    pointer-events: auto;
    z-index: 1;
}

.login-content {
    background: white;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 450px;
    text-align: center;
    pointer-events: auto;
    position: relative;
    z-index: 2;
}

.logo-section {
    margin-bottom: 2.5rem;
}

.logo-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 1rem 0 0.5rem;
}

.logo-section p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.social-login {
    margin-bottom: 2rem;
}

.social-btn {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    pointer-events: auto;
    position: relative;
    z-index: 3;
}

.social-icon {
    width: 20px;
    height: 20px;
}

.google-btn {
    background: #4285f4;
    color: white;
}

.google-btn:hover {
    background: #357ae8;
}

.facebook-btn {
    background: #1877f2;
    color: white;
}

.facebook-btn:hover {
    background: #166fe5;
}

.apple-btn {
    background: #000;
    color: white;
}

.apple-btn:hover {
    background: #333;
}

.divider {
    position: relative;
    margin: 2rem 0;
    text-align: center;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: var(--text-secondary);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    pointer-events: auto;
    cursor: text;
    z-index: 3;
    position: relative;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.login-btn {
    width: 100%;
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    pointer-events: auto;
    position: relative;
    z-index: 3;
}

.login-btn:hover {
    background: var(--primary-dark);
}

/* Main App Screen */
#main-screen {
    background: #f8f9fa;
    position: relative;
}

/* Floating Shapes */
.floating-shape {
    position: absolute;
    border-radius: 50%;
    z-index: 0;
    pointer-events: none;
}

.floating-shape-1 {
    width: 180px;
    height: 180px;
    top: -40px;
    right: -40px;
    background: rgba(245, 245, 245, 0.7);
    border: 1px solid #e0e0e0;
    animation: float1 15s ease-in-out infinite;
}

.floating-shape-2 {
    width: 220px;
    height: 220px;
    top: 50%;
    left: -110px;
    background: rgba(245, 245, 245, 0.5);
    border: 1px solid #e8e8e8;
    animation: float2 18s ease-in-out infinite;
}

.floating-shape-3 {
    width: 150px;
    height: 150px;
    bottom: 100px;
    right: -30px;
    background: rgba(245, 245, 245, 0.6);
    border: 1px solid #e8e8e8;
    animation: float3 12s ease-in-out infinite;
}

@keyframes float1 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(20px, -30px) rotate(11.25deg); }
    50% { transform: translate(-20px, 30px) rotate(22.5deg); }
    75% { transform: translate(20px, 30px) rotate(33.75deg); }
}

@keyframes float2 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-30px, -40px) rotate(-15deg); }
    50% { transform: translate(30px, 40px) rotate(-30deg); }
    75% { transform: translate(-30px, 40px) rotate(-45deg); }
}

@keyframes float3 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-15px, -25px) rotate(22.5deg); }
    50% { transform: translate(15px, 25px) rotate(45deg); }
    75% { transform: translate(-15px, 25px) rotate(67.5deg); }
}

.app-header {
    background: white;
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 10;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.app-title {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.nav-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: var(--bg-secondary);
}

.nav-icon {
    width: 28px;
    height: 28px;
    color: #555;
}

.profile-icon-container {
    width: 28px;
    height: 28px;
    border-radius: 14px;
    overflow: hidden;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-icon-image {
    width: 100%;
    height: 100%;
    border-radius: 14px;
    object-fit: cover;
}

.default-profile-icon {
    width: 20px;
    height: 20px;
}

.main-content {
    height: calc(100vh - 80px);
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* View Management */
.view {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    overflow-y: auto;
}

.view.active {
    opacity: 1;
    visibility: visible;
}

/* Home View */
#home-view {
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    height: 100%;
    position: relative;
}

/* Welcome Section */
.welcome-section {
    padding: 25px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.welcome-text {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 12px;
    color: #333;
}

.instruction-text {
    font-size: 16px;
    color: #666;
    line-height: 22px;
}

/* Shake Section */
.shake-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
    position: relative;
    z-index: 2;
}

.shake-container {
    text-align: center;
}

.shake-outer-circle {
    width: 180px;
    height: 180px;
    border-radius: 90px;
    background: #f8f8f8;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1.5px solid #e0e0e0;
    box-shadow: 0 6px 8px rgba(0,0,0,0.1);
    margin: 0 auto 20px;
    transition: transform 0.3s ease;
}

.shake-button {
    width: 150px;
    height: 150px;
    border-radius: 75px;
    background: #4e9af1;
    border: none;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 6px rgba(78, 154, 241, 0.3);
}

.shake-button:hover .shake-outer-circle {
    transform: scale(1.05);
}

.shake-button:active .shake-outer-circle {
    transform: scale(0.95);
}

.shake-button.shaking .shake-outer-circle {
    animation: shake 0.5s ease-in-out;
}

.shake-icon {
    width: 50px;
    height: 50px;
}

.shake-text {
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 1.5px;
}

.bubble-container {
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.searching-bubble {
    background: rgba(78, 154, 241, 0.1);
    padding: 10px 20px;
    border-radius: 30px;
    border: 1px solid rgba(78, 154, 241, 0.3);
}

.searching-text {
    font-size: 16px;
    color: #4e9af1;
    font-weight: 500;
    margin: 0;
}

/* Matches Section */
.matches-section {
    flex: 1;
    background: transparent;
    margin: 0 20px 20px;
    padding: 0;
    position: relative;
    z-index: 2;
}

.section-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    margin-top: 5px;
    margin-left: 5px;
}

.matches-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.no-matches {
    text-align: center;
    padding: 40px;
    border-radius: 15px;
    background: #f8fafc;
    margin-top: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    color: #999;
}

.no-matches-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
    color: #ccc;
}

.no-matches p {
    font-size: 16px;
    line-height: 22px;
    margin: 0;
}

.match-card {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8fafc;
    border-radius: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: var(--transition);
    cursor: pointer;
    border: 1px solid transparent;
}

.match-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.match-avatar {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    background: #4e9af1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-right: 15px;
    overflow: hidden;
    box-shadow: 0 3px 4px rgba(78, 154, 241, 0.2);
}

.match-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 25px;
}

.match-info {
    flex: 1;
}

.match-name-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.match-info h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c384a;
    margin: 0;
}

.friend-badge {
    display: flex;
    align-items: center;
    gap: 3px;
    background: rgba(78, 154, 241, 0.1);
    color: #4e9af1;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.friend-badge svg {
    width: 12px;
    height: 12px;
    fill: currentColor;
}

.match-distance {
    font-size: 14px;
    color: #8a9cb0;
    margin: 0;
}

.match-actions {
    display: flex;
    gap: 10px;
}

.chat-button {
    width: 45px;
    height: 45px;
    border-radius: 22.5px;
    background: #f0f8ff;
    border: 1px solid #e8eef4;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.chat-button:hover {
    background: #e8f4ff;
}

.chat-button svg {
    width: 24px;
    height: 24px;
    color: #4e9af1;
}

/* Chat View */
#chat-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    background: white;
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 10;
}

.back-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    background: var(--bg-secondary);
}

.back-btn svg {
    width: 24px;
    height: 24px;
    color: #555;
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    flex: 1;
    margin-left: 12px;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4e9af1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: white;
    overflow: hidden;
    font-size: 18px;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chat-username {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.chat-header-actions {
    display: flex;
    align-items: center;
}

.chat-action-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-action-btn:hover {
    background: var(--bg-secondary);
}

.chat-action-btn svg {
    width: 20px;
    height: 20px;
    color: #555;
}

.chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: #f8f9fa;
}

.chat-empty-state,
.chat-loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-style: italic;
}

.chat-loading-state {
    color: #4e9af1;
}

.message {
    display: flex;
    max-width: 70%;
    margin-bottom: 8px;
}

.message.own {
    align-self: flex-end;
    justify-content: flex-end;
}

.message.other {
    align-self: flex-start;
    justify-content: flex-start;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
    max-width: 100%;
}

.message.own .message-bubble {
    background: #4e9af1;
    color: white;
    border-bottom-right-radius: 6px;
}

.message.other .message-bubble {
    background: white;
    color: #333;
    border-bottom-left-radius: 6px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
    display: block;
}

.chat-input-container {
    background: white;
    border-top: 1px solid #eaeaea;
    padding: 12px 16px;
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
    resize: none;
    outline: none;
    transition: var(--transition);
    font-family: inherit;
    min-height: 20px;
    max-height: 100px;
}

.chat-input:focus {
    border-color: #4e9af1;
}

.send-btn {
    background: #4e9af1;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
}

.send-btn:hover {
    background: #3a7bc8;
}

.send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.send-btn svg {
    width: 20px;
    height: 20px;
}

/* Profile View */
#profile-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.profile-header {
    background: white;
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 10;
}

.profile-header h2 {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.save-btn {
    background: #4e9af1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.save-btn:hover {
    background: #3a7bc8;
}

.save-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.profile-content {
    flex: 1;
    overflow-y: auto;
    background: #f8f9fa;
}

.profile-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}

.profile-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.section-title {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 16px;
}

.section-subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
}

/* Photo Grid */
.photo-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.photo-slot {
    aspect-ratio: 1;
    border: 2px dashed #ddd;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.photo-slot:hover {
    border-color: #4e9af1;
}

.photo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 12px;
}

.photo-placeholder svg {
    width: 24px;
    height: 24px;
    margin-bottom: 8px;
}

.photo-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Form Elements */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: var(--transition);
    font-family: inherit;
}

.form-input:focus {
    outline: none;
    border-color: #4e9af1;
}

.form-input select {
    cursor: pointer;
}

/* Passions */
.passions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.passion-chip {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    background: white;
    color: #333;
}

.passion-chip:hover {
    border-color: #4e9af1;
}

.passion-chip.selected {
    background: #4e9af1;
    color: white;
    border-color: #4e9af1;
}

.passion-status {
    font-size: 12px;
    color: #666;
}

.passion-status.warning {
    color: #ff6b6b;
}

.passion-status.success {
    color: #4e9af1;
}

/* Settings View */
#settings-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.settings-header {
    background: white;
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 10;
}

.settings-header h2 {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.settings-content {
    flex: 1;
    overflow-y: auto;
    background: #f8f9fa;
}

.settings-section {
    background: white;
    margin: 20px;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.settings-section:first-child {
    margin-top: 20px;
}

.settings-section:last-child {
    margin-bottom: 20px;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.setting-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.setting-value {
    font-weight: 600;
    color: #4e9af1;
    font-size: 14px;
}

.setting-status {
    font-size: 12px;
    color: #666;
}

.setting-action-btn {
    background: #4e9af1;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.setting-action-btn:hover {
    background: #3a7bc8;
}

.setting-action-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Gender and preference styles for main settings */
.settings-section .gender-container {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.settings-section .gender-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: 2px solid #4e9af1;
    background: white;
    color: #4e9af1;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.settings-section .gender-button:hover {
    background: rgba(78, 154, 241, 0.1);
}

.settings-section .gender-button.selected {
    background: #4e9af1;
    color: white;
}

.settings-section .gender-button svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.settings-section .custom-toggle {
    display: flex;
    background: #f0f0f0;
    border-radius: 20px;
    padding: 3px;
    gap: 3px;
}

.settings-section .toggle-option {
    padding: 8px 12px;
    border-radius: 17px;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-section .toggle-option:hover {
    background: rgba(78, 154, 241, 0.1);
}

.settings-section .toggle-option.active {
    background: #4e9af1;
    color: white;
}

.settings-section .toggle-option svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* Sliders */
.slider-container {
    margin: 16px 0;
}

.slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4e9af1;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4e9af1;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4e9af1;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Blocked Users */
.blocked-users-list {
    margin-top: 12px;
}

.no-blocked-users {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.blocked-user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 8px;
}

.blocked-user-info {
    font-weight: 600;
    color: #333;
}

.unblock-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.unblock-btn:hover {
    background: #ff5252;
}

/* Logout Button */
.logout-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    justify-content: center;
}

.logout-btn:hover {
    background: #ff5252;
}

.logout-btn svg {
    width: 18px;
    height: 18px;
}

/* Settings Dropdown Styles */
.settings-dropdown-container {
    position: relative;
}

.settings-dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e0e0;
    min-width: 320px;
    max-width: 400px;
    max-height: 80vh;
    z-index: 1000;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.settings-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.settings-dropdown-content {
    max-height: 80vh;
    overflow-y: auto;
    padding: 0;
}

.settings-dropdown-section {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.settings-dropdown-section:last-child {
    border-bottom: none;
}

.settings-dropdown-section .section-title {
    font-size: 16px;
    font-weight: 700;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-dropdown-section .section-icon {
    width: 18px;
    height: 18px;
    fill: currentColor;
}

.settings-dropdown-section .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    margin-bottom: 8px;
}

.settings-dropdown-section .setting-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.settings-dropdown-section .setting-label {
    font-weight: 600;
    color: #333;
    font-size: 13px;
}

.settings-dropdown-section .setting-value {
    font-weight: 600;
    color: #4e9af1;
    font-size: 13px;
}

.settings-dropdown-section .slider-container {
    margin: 8px 0 12px 0;
}

.settings-dropdown-section .slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.settings-dropdown-section .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4e9af1;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.settings-dropdown-section .slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4e9af1;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.settings-dropdown-section .slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    font-size: 11px;
    color: #999;
}

.settings-dropdown-section .slider-label {
    font-size: 12px;
    color: #666;
    margin: 4px 0;
    font-weight: 500;
}

.settings-dropdown-section .logout-btn {
    width: 100%;
    background: #ff3b30;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.settings-dropdown-section .logout-btn:hover {
    background: #d70015;
}

.settings-dropdown-section .logout-btn svg {
    width: 16px;
    height: 16px;
}

/* Premium dropdown styles */
.settings-dropdown-section.premium-section {
    background: linear-gradient(135deg, #fff9e6 0%, #ffeaa7 100%);
    border: 2px solid #ffd700;
    border-radius: 12px;
    margin: 8px;
    padding: 16px;
}

.settings-dropdown-section.premium-section .premium-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.settings-dropdown-section.premium-section .premium-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #ffd700;
    color: #000;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
}

.settings-dropdown-section.premium-section .premium-icon {
    width: 12px;
    height: 12px;
    fill: currentColor;
}

/* Gender and preference styles for dropdown */
.settings-dropdown-section .gender-container {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.settings-dropdown-section .gender-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 12px;
    border: 2px solid #4e9af1;
    background: white;
    color: #4e9af1;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.settings-dropdown-section .gender-button:hover {
    background: rgba(78, 154, 241, 0.1);
}

.settings-dropdown-section .gender-button.selected {
    background: #4e9af1;
    color: white;
}

.settings-dropdown-section .gender-button svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

.settings-dropdown-section .custom-toggle {
    display: flex;
    background: #f0f0f0;
    border-radius: 16px;
    padding: 2px;
    gap: 2px;
}

.settings-dropdown-section .toggle-option {
    padding: 6px 10px;
    border-radius: 14px;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-dropdown-section .toggle-option:hover {
    background: rgba(78, 154, 241, 0.1);
}

.settings-dropdown-section .toggle-option.active {
    background: #4e9af1;
    color: white;
}

.settings-dropdown-section .toggle-option svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

/* Responsive dropdown styles */
@media (max-width: 768px) {
    .settings-dropdown {
        right: -10px;
        left: 10px;
        min-width: auto;
        max-width: calc(100vw - 20px);
    }

    .settings-dropdown-section {
        padding: 12px 16px;
    }

    .settings-dropdown-section .section-title {
        font-size: 15px;
    }

    .settings-dropdown-section .setting-label,
    .settings-dropdown-section .setting-value {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .settings-dropdown {
        right: -5px;
        left: 5px;
        max-height: 70vh;
    }

    .settings-dropdown-section {
        padding: 10px 12px;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-close-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.modal-close-btn svg {
    width: 24px;
    height: 24px;
    color: #333;
}

/* Profile Modal */
.profile-modal-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .profile-modal-content {
    transform: scale(1);
}

.profile-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eaeaea;
    background: white;
}

.profile-modal-header h2 {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.profile-options-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-options-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.profile-options-btn svg {
    width: 20px;
    height: 20px;
    color: #333;
}

.profile-modal-body {
    overflow-y: auto;
    max-height: calc(90vh - 80px);
}

/* Profile Photo Section */
.profile-photo-section {
    position: relative;
    background: #f8f9fa;
}

.profile-photo-container {
    aspect-ratio: 1;
    position: relative;
    overflow: hidden;
}

.profile-photo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
}

.profile-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 60px;
    background: #4e9af1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    font-weight: 700;
    color: white;
}

.profile-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Photo Navigation */
.photo-nav {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 10;
}

.photo-nav-btn {
    background: rgba(0, 0, 0, 0.6);
    border: none;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.photo-nav-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.photo-nav-btn:active {
    transform: scale(0.95);
}

.photo-nav-btn svg {
    width: 24px;
    height: 24px;
}

.photo-nav-btn.next svg {
    transform: rotate(180deg);
}

.photo-indicators {
    display: flex;
    gap: 8px;
}

.photo-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
}

.photo-indicator.active {
    background: white;
}

/* Profile Info Section */
.profile-info-section {
    padding: 20px;
}

.profile-name-row {
    margin-bottom: 16px;
}

.profile-name-row h3 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.profile-description-section {
    margin-bottom: 20px;
}

.profile-description-section p {
    font-size: 16px;
    line-height: 1.5;
    color: #666;
    margin: 0;
}

.profile-passions-section h4 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
}

.profile-passion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.profile-passion-tag {
    background: #4e9af1;
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
}

/* Chat Options Modal */
.chat-options-content {
    background: white;
    border-radius: 16px;
    width: 280px;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .chat-options-content {
    transform: scale(1);
}

.chat-options-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eaeaea;
}

.chat-options-header h3 {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.chat-options-list {
    padding: 8px 0;
}

.chat-option-item {
    width: 100%;
    background: none;
    border: none;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 16px;
    color: #333;
}

.chat-option-item:hover {
    background: #f8f9fa;
}

.chat-option-item.danger {
    color: #ff6b6b;
}

.chat-option-item.danger:hover {
    background: #fff5f5;
}

.chat-option-item svg {
    width: 20px;
    height: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header {
        padding: 1rem;
    }

    .header-content {
        padding: 0;
    }

    #home-view {
        padding: 1rem;
    }

    .shake-button {
        width: 150px;
        height: 150px;
    }

    .matches-list {
        grid-template-columns: 1fr;
    }

    .chat-header,
    .profile-header,
    .settings-header {
        padding: 1rem;
    }

    .chat-messages,
    .chat-input-container {
        padding: 1rem;
    }

    .profile-content,
    .settings-content {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.match-card {
    animation: fadeIn 0.5s ease-out;
}

.message {
    animation: fadeIn 0.3s ease-out;
}

/* Shake Animation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake-button.shaking {
    animation: shake 0.5s ease-in-out;
}

/* Premium Styles */
.premium-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    margin-left: 8px;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.premium-badge .premium-icon {
    width: 12px;
    height: 12px;
    margin-right: 4px;
    fill: currentColor;
}

.premium-section {
    border: 2px solid #FFD700;
    background: linear-gradient(135deg, #fffbf0, #fff8e1);
    position: relative;
    overflow: hidden;
}

.premium-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #FFD700, #FFA500, #FFD700);
}

.premium-section .section-title {
    color: #B8860B;
    display: flex;
    align-items: center;
    gap: 8px;
}

.premium-section .section-icon {
    width: 20px;
    height: 20px;
    fill: #FFD700;
}

.premium-info {
    background: rgba(255, 215, 0, 0.1);
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.premium-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;
}

.premium-badge-large {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
}

.premium-badge-large .premium-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    fill: currentColor;
}

.premium-days {
    color: #B8860B;
    font-size: 14px;
    font-weight: 500;
}

.premium-unlimited {
    color: #228B22;
    font-size: 14px;
    font-weight: 600;
}

.premium-feature {
    border-top: 1px solid rgba(255, 215, 0, 0.3);
    padding-top: 20px;
    margin-top: 20px;
}

.feature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.feature-icon {
    width: 18px;
    height: 18px;
    fill: var(--primary-color);
}

.feature-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    margin-bottom: 16px;
    line-height: 1.5;
}

.custom-location-active {
    background: rgba(34, 139, 34, 0.1);
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #228B22;
}

.location-text {
    color: #228B22;
    font-weight: 500;
    margin-bottom: 12px;
}

.location-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.custom-location-inactive {
    padding: 16px;
}

/* Location Modal */
.location-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.location-modal.active {
    opacity: 1;
    visibility: visible;
}

.location-modal .modal-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.location-modal.active .modal-content {
    transform: scale(1);
}

.location-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.location-modal .modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.location-modal .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.location-modal .close-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.location-modal .modal-body {
    padding: 20px;
}

.location-modal .modal-description {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.5;
}

.location-modal .input-group {
    margin-bottom: 16px;
}

.location-modal .input-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--text-primary);
}

.location-modal .input-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.location-modal .input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(78, 154, 241, 0.1);
}

.location-modal .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* Location Setup Buttons */
.location-setup-buttons {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

.location-setup-buttons .btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* Map Location Modal */
.map-location-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.map-location-modal.active {
    opacity: 1;
    visibility: visible;
}

.map-modal-content {
    width: 90%;
    max-width: 900px;
    height: 85vh;
    max-height: 700px;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Map Modal Header */
.map-location-modal .modal-header {
    background: var(--primary-color);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: none;
}

.map-location-modal .modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.map-location-modal .close-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.2s ease;
}

.map-location-modal .close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.map-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.location-map {
    flex: 1;
    background: #f0f0f0;
    position: relative;
    overflow: hidden;
    min-height: 400px;
}

.simple-map {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #e8f4f8, #d4e8f0);
    position: relative;
    cursor: crosshair;
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-overlay {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.map-overlay h4 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
}

.map-overlay p {
    margin: 0 0 12px 0;
    color: var(--text-secondary);
}

.selected-coords {
    font-size: 12px;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 8px;
    border-radius: 4px;
    margin-top: 8px;
}

.map-marker {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border: 3px solid white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    animation: markerPulse 2s infinite;
}

@keyframes markerPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
}

.map-controls {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    justify-content: center;
}

#reset-location-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 14px 20px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(78, 154, 241, 0.3);
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    white-space: nowrap;
}

#reset-location-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 154, 241, 0.4);
}

#reset-location-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(78, 154, 241, 0.3);
}

#reset-location-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    background: #6c757d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Header Location Indicator */
.location-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-left: 16px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    font-size: 12px;
}

.location-icon {
    width: 14px;
    height: 14px;
    fill: var(--text-secondary);
}

.location-icon.premium {
    fill: #FFD700;
}

.location-text {
    color: var(--text-secondary);
    font-weight: 500;
}

.location-text.premium {
    color: #B8860B;
    font-weight: 600;
}

.premium-badge-small {
    display: flex;
    align-items: center;
    gap: 2px;
    background: #FFD700;
    color: #000;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
}

.premium-badge-small .premium-icon {
    width: 10px;
    height: 10px;
    fill: currentColor;
}

/* Premium Section Header */
.premium-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.premium-header-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.refresh-btn {
    padding: 8px;
    border-radius: 20px;
    background: rgba(78, 154, 241, 0.1);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.refresh-btn:hover {
    background: rgba(78, 154, 241, 0.2);
}

.refresh-btn svg {
    width: 16px;
    height: 16px;
    fill: #4e9af1;
}

/* Premium Content */
.premium-loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.premium-upgrade {
    text-align: center;
}

.upgrade-info h4 {
    color: #B8860B;
    margin-bottom: 15px;
    font-size: 18px;
}

.premium-features-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.premium-features-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    color: #666;
}

.premium-features-list svg {
    width: 16px;
    height: 16px;
    fill: #FFD700;
}

.upgrade-btn {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    transition: all 0.2s ease;
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
}

.upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4);
}

.upgrade-btn svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* Feature sections */
.feature-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.feature-header h4 {
    margin: 0;
    color: #B8860B;
    font-size: 16px;
}

.feature-icon {
    width: 20px;
    height: 20px;
    fill: #FFD700;
}

.custom-location-active,
.custom-location-inactive {
    padding: 15px;
    background: rgba(255, 215, 0, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.location-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.change-location-btn,
.enable-location-btn {
    background: #4e9af1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.change-location-btn:hover,
.enable-location-btn:hover {
    background: #3d8ce6;
}

.change-location-btn svg,
.enable-location-btn svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

.disable-location-btn {
    background: transparent;
    color: #666;
    border: 1px solid #ddd;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.disable-location-btn:hover {
    background: #f5f5f5;
    border-color: #ccc;
}

/* Auth warning */
.auth-warning {
    text-align: center;
    padding: 20px;
}

.auth-warning h4 {
    color: #FF3B30;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.warning-icon {
    width: 20px;
    height: 20px;
    fill: #FF3B30;
}

.auth-warning p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.5;
}

.login-btn {
    background: #FF3B30;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    transition: all 0.2s ease;
}

.login-btn:hover {
    background: #e6342a;
}

.login-btn svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* Match Options Styles */
.match-options-container {
    position: relative;
}

.match-options-btn {
    background: transparent;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.match-options-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.match-options-btn svg {
    width: 16px;
    height: 16px;
    fill: #666;
}

.match-options-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e0e0;
    min-width: 160px;
    z-index: 1000;
    overflow: hidden;
}

.match-option-item {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: all 0.2s ease;
    text-align: left;
}

.match-option-item:hover {
    background: #f5f5f5;
}

.match-option-item.danger {
    color: #FF3B30;
}

.match-option-item.danger:hover {
    background: rgba(255, 59, 48, 0.1);
}

.match-option-item svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.match-option-item span {
    flex: 1;
}

/* Update match actions to accommodate the options button */
.match-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Close options menu when clicking outside */
.match-options-menu.show {
    display: block;
}

/* Map Modal Footer */
.map-location-modal .modal-footer {
    background: #f8f9fa;
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #e9ecef;
}

.map-location-modal .btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    font-size: 14px;
}

.map-location-modal .btn-secondary {
    background: #6c757d;
    color: white;
}

.map-location-modal .btn-secondary:hover {
    background: #5a6268;
}

.map-location-modal .btn-primary {
    background: var(--primary-color);
    color: white;
}

.map-location-modal .btn-primary:hover {
    background: var(--primary-dark);
}

.map-location-modal .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive Design for Map Modal */
@media (max-width: 768px) {
    .map-modal-content {
        width: 95%;
        height: 90vh;
        margin: 5vh auto;
    }

    .map-controls {
        top: 15px;
        left: 50%;
        transform: translateX(-50%);
    }

    #reset-location-btn {
        padding: 12px 16px;
        font-size: 13px;
    }

    .map-location-modal .modal-header {
        padding: 16px;
    }

    .map-location-modal .modal-footer {
        padding: 16px;
        flex-direction: column;
        gap: 8px;
    }

    .map-location-modal .btn {
        width: 100%;
        padding: 12px;
    }
}

/* Leaflet Map Styles */
.leaflet-container {
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
}

/* Style the default Leaflet controls */
.leaflet-control-zoom {
    border: none !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    border-radius: 12px !important;
    overflow: hidden;
}

.leaflet-control-zoom a {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #333 !important;
    border: none !important;
    width: 36px !important;
    height: 36px !important;
    line-height: 36px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease !important;
}

.leaflet-control-zoom a:hover {
    background: rgba(255, 255, 255, 1) !important;
    color: #000 !important;
}

.leaflet-control-zoom a:first-child {
    border-top-left-radius: 12px !important;
    border-top-right-radius: 12px !important;
}

.leaflet-control-zoom a:last-child {
    border-bottom-left-radius: 12px !important;
    border-bottom-right-radius: 12px !important;
}

/* Style the attribution control */
.leaflet-control-attribution {
    background: rgba(255, 255, 255, 0.8) !important;
    border-radius: 8px !important;
    font-size: 11px !important;
    backdrop-filter: blur(10px);
}

/* Verification Styles */
.verification-instruction-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
}

.verification-instruction-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.verification-instruction-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.verification-instruction-body {
    padding: 30px;
    text-align: center;
}

.verification-instruction-text {
    font-size: 18px;
    color: #666;
    margin-bottom: 30px;
}

.verification-gesture-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
}

.verification-hand-gesture {
    font-size: 100px;
    line-height: 1;
}

.verification-face-gesture {
    font-size: 100px;
    line-height: 1;
}

.verification-finger-count {
    font-size: 24px;
    font-weight: 600;
    color: #4e9af1;
    margin-bottom: 10px;
}

.verification-instruction-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
}

.verification-instruction-buttons {
    display: flex;
    gap: 15px;
    padding: 20px;
}

.verification-instruction-buttons .btn {
    flex: 1;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.verification-instruction-buttons .btn-secondary {
    background: #f0f0f0;
    color: #666;
}

.verification-instruction-buttons .btn-primary {
    background: #4e9af1;
    color: white;
}

.verification-photo-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
}

.verification-photo-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.verification-photo-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.verification-photo-body {
    padding: 20px;
}

.verification-photo-preview {
    text-align: center;
    margin-bottom: 20px;
}

.verification-photo-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.verification-photo-tips h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.verification-tips-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.verification-tip {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: #666;
}

.verification-tip-icon {
    font-size: 16px;
}

.verification-photo-buttons {
    display: flex;
    gap: 15px;
    padding: 20px;
}

.verification-photo-buttons .btn {
    flex: 1;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.verification-photo-buttons .btn-secondary {
    background: #f0f0f0;
    color: #666;
}

.verification-photo-buttons .btn-primary {
    background: #4e9af1;
    color: white;
}

/* Verification section in profile */
.verification-status {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    border-radius: 12px;
    margin-bottom: 15px;
}

.verification-status.verified {
    background: #f0f9ff;
    border: 1px solid #bfdbfe;
}

.verification-status.not-verified {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
}

.verification-status.rejected {
    background: #fef2f2;
    border: 1px solid #fecaca;
}

.verification-icon {
    width: 24px;
    height: 24px;
}

.verification-text {
    flex: 1;
}

.verification-title {
    font-weight: 600;
    margin: 0 0 4px 0;
}

.verification-description {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.verification-button {
    background: #4e9af1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
}

.verification-button:hover {
    background: #3b82f6;
}

/* Rejection UI styles */
.rejection-details {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 12px;
    padding: 20px;
    margin-top: 15px;
}

.rejection-requirement {
    text-align: center;
    margin-bottom: 15px;
}

.rejection-finger-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: #f3f4f6;
    padding: 10px 15px;
    border-radius: 8px;
    margin-top: 8px;
}

.rejection-finger-emoji {
    font-size: 32px;
}

.rejection-finger-text {
    font-size: 16px;
    font-weight: 600;
    color: #4e9af1;
}

.rejection-feedback {
    margin-bottom: 15px;
}

.rejection-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: block;
}

.rejection-notes {
    font-size: 14px;
    color: #dc2626;
    background: #fef2f2;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #dc2626;
}

.rejection-tips {
    margin-bottom: 20px;
}

.rejection-tips-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rejection-tip {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rejection-tip-emoji {
    font-size: 16px;
}

.rejection-tip-text {
    font-size: 14px;
    color: #6b7280;
}
