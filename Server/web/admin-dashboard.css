/* Admin Dashboard Styles - Professional Dark Theme */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --bg-primary: #0b0e27;
    --bg-secondary: #151837;
    --bg-card: #1a1d3e;
    --bg-card-hover: #1f2347;
    --bg-sidebar: #0f1332;
    --text-primary: #ffffff;
    --text-secondary: #a0a9c3;
    --text-muted: #6b7394;
    --accent-primary: #4a7dff;
    --accent-secondary: #00d4ff;
    --accent-success: #00c896;
    --accent-warning: #ffa726;
    --accent-danger: #ff5252;
    --accent-purple: #a855f7;
    --border-color: #2a2f4e;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-blue: linear-gradient(135deg, #4a7dff 0%, #00d4ff 100%);
    --gradient-purple: linear-gradient(135deg, #a855f7 0%, #6366f1 100%);
    --gradient-success: linear-gradient(135deg, #00c896 0%, #00d68f 100%);
    --chart-blue: #00d4ff;
    --chart-purple: #a855f7;
    --chart-orange: #ff9f43;
    --chart-green: #00c896;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}

/* Layout */
.app-layout {
    display: flex;
    min-height: 100vh;
    background: var(--bg-primary);
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: var(--bg-sidebar);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: all 0.3s ease;
    border-right: 1px solid var(--border-color);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-blue);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.logo-icon::before {
    content: '';
    width: 20px;
    height: 20px;
    background: white;
    mask: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMiA3TDEyIDEyTDIyIDdMMTIgMloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yIDEyTDEyIDE3TDIyIDEyIiBmaWxsPSJ3aGl0ZSIgb3BhY2l0eT0iMC42Ii8+CjxwYXRoIGQ9Ik0yIDE3TDEyIDIyTDIyIDE3IiBmaWxsPSJ3aGl0ZSIgb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPg==') no-repeat center;
    mask-size: contain;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* Navigation Menu */
.nav-menu {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 1rem;
}

.nav-section-title {
    padding: 0.75rem 1.5rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--text-muted);
    font-weight: 600;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    padding: 0.875rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: 0.925rem;
    position: relative;
}

.nav-item:hover {
    background: rgba(74, 125, 255, 0.1);
    color: var(--text-primary);
}

.nav-item.active {
    background: rgba(74, 125, 255, 0.15);
    color: var(--accent-primary);
    border-left: 3px solid var(--accent-primary);
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Modern SVG Icon System */
.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

/* Icon size variants */
.icon-sm { width: 16px; height: 16px; }
.icon-md { width: 20px; height: 20px; }
.icon-lg { width: 24px; height: 24px; }
.icon-xl { width: 28px; height: 28px; }

/* Navigation icons */
.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.nav-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

/* Stat card icons */
.stat-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 260px;
    background: var(--bg-primary);
    min-height: 100vh;
}

/* Top Bar */
.top-bar {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.breadcrumb-separator {
    color: var(--text-muted);
}

.breadcrumb-current {
    color: var(--accent-primary);
}

/* Search Bar */
.search-bar {
    position: relative;
    width: 350px;
}

.search-input {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.625rem 1rem 0.625rem 2.5rem;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(74, 125, 255, 0.1);
}

.search-bar .icon {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

/* Top Bar Actions */
.top-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.action-btn:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.notification-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: var(--accent-danger);
    border-radius: 50%;
    border: 2px solid var(--bg-secondary);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-profile:hover {
    background: var(--bg-card);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    overflow: hidden;
    background: var(--gradient-purple);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Content Area */
.content-area {
    padding: 2rem;
}

/* Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4e9af1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Profile and Messages Modal Styles */
.profile-view {
    font-family: inherit;
}

.profile-photos img {
    border-radius: 12px;
}

.btn-info {
    background: #17a2b8;
    color: white;
    border: 1px solid #17a2b8;
}

.btn-info:hover {
    background: #138496;
    border-color: #117a8b;
}

/* App-like profile and messages styles */
.app-profile-view {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-photo-section {
    border-radius: 0;
}

.app-info-section {
    position: relative;
    z-index: 1;
}

/* Message bubbles */
.message-bubble {
    word-wrap: break-word;
    word-break: break-word;
}

/* Modal adjustments for app-like appearance - no borders */
#userProfileModal .modal {
    padding: 0;
}

#userProfileModal .modal > div {
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

#userMessagesModal .modal {
    padding: 0;
}

#userMessagesModal .modal > div {
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Welcome Section */
.welcome-section {
    background: var(--gradient-blue);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 200px;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.welcome-content {
    position: relative;
    z-index: 1;
}

.welcome-text h2 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.welcome-text p {
    opacity: 0.95;
    font-size: 1rem;
}

.welcome-stats {
    display: flex;
    gap: 4rem;
}

.welcome-stat {
    text-align: center;
}

.welcome-stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.welcome-stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.welcome-illustration {
    position: relative;
    z-index: 1;
}

.welcome-illustration img {
    max-height: 180px;
    width: auto;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-title {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.stat-icon.users { background: rgba(74, 125, 255, 0.2); color: var(--accent-primary); }
.stat-icon.active { background: rgba(0, 212, 255, 0.2); color: var(--accent-secondary); }
.stat-icon.revenue { background: rgba(168, 85, 247, 0.2); color: var(--accent-purple); }
.stat-icon.growth { background: rgba(255, 159, 67, 0.2); color: var(--accent-warning); }

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.stat-change.positive { color: var(--accent-success); }
.stat-change.negative { color: var(--accent-danger); }

.change-arrow {
    font-size: 0.75rem;
}

/* Chart Cards */
.charts-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-options {
    display: flex;
    gap: 0.5rem;
}

.chart-option-btn {
    padding: 0.375rem 0.875rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.chart-option-btn.active {
    background: var(--accent-primary);
    color: white;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Bar Chart Styles */
.bar-chart {
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
    height: 100%;
    padding: 20px;
}

.bar {
    width: 30px;
    background: var(--gradient-blue);
    border-radius: 4px 4px 0 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.bar:hover {
    transform: translateY(-5px);
    filter: brightness(1.2);
}

/* Circular Progress */
.circular-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.progress-ring {
    position: relative;
    width: 180px;
    height: 180px;
    margin-bottom: 1.5rem;
}

.progress-ring svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-ring circle {
    fill: none;
    stroke-width: 12;
}

.progress-bg {
    stroke: var(--bg-secondary);
}

.progress-fill {
    stroke: url(#gradientChart);
    stroke-dasharray: 502;
    stroke-dashoffset: 151;
    stroke-linecap: round;
    transition: stroke-dashoffset 1s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.progress-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.progress-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Device Legend */
.device-legend {
    display: flex;
    flex-direction: column;
    gap: 0.875rem;
    width: 100%;
    max-width: 200px;
}

.legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
}

.legend-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
}

.legend-dot {
    width: 10px;
    height: 10px;
    border-radius: 2px;
}

.legend-value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Data Tables */
.data-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
}

.section-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th {
    background: var(--bg-secondary);
    color: var(--text-muted);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem 1.5rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

td {
    padding: 1rem 1.5rem;
    color: var(--text-primary);
    border-bottom: 1px solid rgba(42, 47, 78, 0.5);
}

tr:hover td {
    background: rgba(74, 125, 255, 0.05);
}

/* Badges */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
}

.badge-success {
    background: rgba(0, 200, 150, 0.2);
    color: var(--accent-success);
}

.badge-warning {
    background: rgba(255, 167, 38, 0.2);
    color: var(--accent-warning);
}

.badge-danger {
    background: rgba(255, 82, 82, 0.2);
    color: var(--accent-danger);
}

.badge-info {
    background: rgba(74, 125, 255, 0.2);
    color: var(--accent-primary);
}

.badge-premium {
    background: rgba(168, 85, 247, 0.2);
    color: var(--accent-purple);
}

/* Status badges for verification */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    text-transform: capitalize;
}

.status-badge.pending {
    background: rgba(255, 167, 38, 0.2);
    color: var(--accent-warning);
}

.status-badge.approved {
    background: rgba(0, 200, 150, 0.2);
    color: var(--accent-success);
}

.status-badge.rejected {
    background: rgba(255, 82, 82, 0.2);
    color: var(--accent-danger);
}

.status-badge.success {
    background: rgba(0, 200, 150, 0.2);
    color: var(--accent-success);
}

.status-badge.warning {
    background: rgba(255, 167, 38, 0.2);
    color: var(--accent-warning);
}

.status-badge.danger {
    background: rgba(255, 82, 82, 0.2);
    color: var(--accent-danger);
}

/* Verification badge icons */
.verification-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
}

.verification-badge.blue {
    background: rgba(74, 125, 255, 0.2);
    color: var(--accent-primary);
}

.verification-badge.gold {
    background: rgba(255, 167, 38, 0.2);
    color: var(--accent-warning);
}

/* Buttons */
.btn {
    padding: 0.625rem 1.25rem;
    border: none;
    border-radius: 10px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--gradient-blue);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 125, 255, 0.3);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.15rem;
}

.btn-danger {
    background: var(--accent-danger);
    color: white;
}

.btn-danger:hover {
    background: #ff3838;
}

.btn-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    font-size: 14px;
    margin: 0 2px;
}

.action-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: flex-start;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-card);
    margin: 5% auto;
    padding: 2rem;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    border: 1px solid var(--border-color);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close {
    color: var(--text-muted);
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
    background: none;
    border: none;
    padding: 0;
}

.close:hover {
    color: var(--accent-danger);
}

/* Forms */
.form-group {
    margin-bottom: 1.25rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

/* Smaller form selects for filters */
.chart-options .form-select {
    width: auto;
    min-width: 120px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(74, 125, 255, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.pagination-info {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Checkbox */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-primary);
    cursor: pointer;
}

/* Loading State */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--text-muted);
    border-top-color: var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .charts-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .welcome-section {
        flex-direction: column;
        text-align: center;
    }

    .welcome-stats {
        flex-direction: column;
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .search-bar {
        display: none;
    }

    .modal-content {
        width: 95%;
        padding: 1.5rem;
    }
}