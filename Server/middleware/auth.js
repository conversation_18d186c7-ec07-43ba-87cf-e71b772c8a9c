// middleware/auth.js - Authentication and authorization middleware
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');

// JWT secret - in production, use environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Rate limiting configurations
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      res.status(429).json({ error: message });
    }
  });
};

// Rate limiters for different operations
const authRateLimit = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts
  'Too many authentication attempts, please try again later'
);

const adminActionRateLimit = createRateLimiter(
  60 * 1000, // 1 minute
  50, // 50 actions (increased from 10 to handle dashboard loading)
  'Too many admin actions, please slow down'
);

const sensitiveActionRateLimit = createRateLimiter(
  5 * 60 * 1000, // 5 minutes
  3, // 3 attempts
  'Too many sensitive actions, please wait before trying again'
);

// Generate JWT token
const generateToken = (user) => {
  const payload = {
    userId: user._id.toString(),
    username: user.username,
    role: user.role,
    iat: Math.floor(Date.now() / 1000)
  };
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};

// Verify JWT token
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

// Extract token from request (cookie or Authorization header)
const extractToken = (req) => {
  // First try to get from cookie
  if (req.cookies && req.cookies.adminToken) {
    return req.cookies.adminToken;
  }
  
  // Then try Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return null;
};

// Authentication middleware - verify user is logged in
const authenticate = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return res.status(401).json({ error: 'Access denied. No token provided.' });
    }
    
    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({ error: 'Invalid token.' });
    }
    
    // Get user from database to ensure they still exist and are active
    const user = await User.findById(decoded.userId).select('-password');
    if (!user) {
      return res.status(401).json({ error: 'User not found.' });
    }
    
    if (user.accountStatus !== 'active') {
      return res.status(401).json({ error: 'Account is not active.' });
    }
    
    if (user.isLocked) {
      return res.status(401).json({ error: 'Account is temporarily locked.' });
    }
    
    // Add user info to request
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ error: 'Authentication failed.' });
  }
};

// Authorization middleware - verify user has admin role
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required.' });
  }
  
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required.' });
  }
  
  next();
};

// Combined authentication and admin authorization
const authenticateAdmin = [authenticate, requireAdmin];

// Input validation middleware
const validateInput = (validations) => {
  return async (req, res, next) => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)));
    
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }
    
    next();
  };
};

// Common validation rules
const validationRules = {
  username: body('username')
    .trim()
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
    
  email: body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Must be a valid email address'),
    
  password: body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('Password must be 6-128 characters long'),
    
  role: body('role')
    .optional()
    .isIn(['user', 'admin'])
    .withMessage('Role must be either user or admin'),
    
  accountStatus: body('accountStatus')
    .optional()
    .isIn(['active', 'blocked', 'deleted'])
    .withMessage('Account status must be active, blocked, or deleted'),
    
  age: body('age')
    .optional()
    .isInt({ min: 18, max: 120 })
    .withMessage('Age must be between 18 and 120'),
    
  description: body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be 500 characters or less'),
    
  passions: body('passions')
    .optional()
    .isArray({ min: 0, max: 10 })
    .withMessage('Passions must be an array with maximum 10 items')
    .custom((passions) => {
      if (passions && passions.some(passion => typeof passion !== 'string' || passion.length > 50)) {
        throw new Error('Each passion must be a string with maximum 50 characters');
      }
      return true;
    })
};

// CSRF protection middleware (simple token-based)
const csrfProtection = (req, res, next) => {
  if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS') {
    return next();
  }
  
  const token = req.headers['x-csrf-token'] || req.body._csrf;
  const sessionToken = req.session?.csrfToken;
  
  if (!token || !sessionToken || token !== sessionToken) {
    return res.status(403).json({ error: 'Invalid CSRF token' });
  }
  
  next();
};

// Audit logging middleware
const auditLog = (action) => {
  return (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log the action after response is sent
      setImmediate(() => {
        const logEntry = {
          timestamp: new Date(),
          action,
          adminUser: req.user?.username || 'unknown',
          adminUserId: req.user?._id?.toString() || 'unknown',
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          success: res.statusCode < 400,
          statusCode: res.statusCode,
          targetUserId: req.params.userId || req.body.userId,
          details: {
            method: req.method,
            url: req.originalUrl,
            body: action.includes('sensitive') ? '[REDACTED]' : req.body
          }
        };
        
        console.log('AUDIT LOG:', JSON.stringify(logEntry));
        // In production, save to database or external logging service
      });
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

module.exports = {
  generateToken,
  verifyToken,
  extractToken,
  authenticate,
  requireAdmin,
  authenticateAdmin,
  validateInput,
  validationRules,
  csrfProtection,
  auditLog,
  rateLimiters: {
    auth: authRateLimit,
    adminAction: adminActionRateLimit,
    sensitiveAction: sensitiveActionRateLimit
  }
};
