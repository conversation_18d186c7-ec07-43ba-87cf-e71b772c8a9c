// screens/ProfileScreen.js
import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,

  TextInput,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,

  Modal,
  Animated,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { profileStyles, theme } from './ScreensStyles';
import { useThemedStyles, useThemeColors } from '../contexts/ThemeContext';
// Removed SVG import - using native components instead

// Predefined passion options
const PASSION_OPTIONS = [
  'Travel', 'Music', 'Movies', 'Reading', 'Cooking', 'Fitness', 'Photography', 'Art',
  'Dancing', 'Gaming', 'Sports', 'Nature', 'Technology', 'Fashion', 'Food', 'Coffee',
  'Wine', 'Hiking', 'Beach', 'Yoga', 'Meditation', 'Writing', 'Learning', 'Animals',
  'Cars', 'Motorcycles', 'Cycling', 'Running', 'Swimming', 'Skiing', 'Surfing', 'Climbing',
  'Comedy', 'Theater', 'Concerts', 'Festivals', 'Volunteering', 'Gardening', 'DIY',
  'Shopping', 'Nightlife', 'Adventure', 'Culture', 'History', 'Science', 'Politics'
];

// Hand gesture components using native React Native elements
const HandGesture = ({ fingers, size = 120, color = '#4e9af1' }) => {
  const getFingerEmoji = (count) => {
    const fingerEmojis = {
      1: '☝️',
      2: '✌️',
      3: '🤟',
      4: '🖖',
      5: '🖐️'
    };
    return fingerEmojis[count] || '☝️';
  };

  return (
    <View style={{
      alignItems: 'center',
      justifyContent: 'center',
      width: size,
      height: size,
      backgroundColor: `rgba(${color.replace('#', '').match(/.{2}/g).map(hex => parseInt(hex, 16)).join(', ')}, 0.1)`,
      borderRadius: size / 2,
      borderWidth: 3,
      borderColor: color,
      borderStyle: 'dashed'
    }}>
      <Text style={{
        fontSize: size * 0.6,
        textAlign: 'center',
        lineHeight: size * 0.7
      }}>
        {getFingerEmoji(fingers)}
      </Text>
    </View>
  );
};

// Enhanced instruction display with face and bigger emoji
const InstructionDisplay = ({ fingers, size = 100 }) => {
  const getFingerEmoji = (count) => {
    const fingerEmojis = {
      1: '☝️',
      2: '✌️',
      3: '🤟',
      4: '🖖',
      5: '🖐️'
    };
    return fingerEmojis[count] || '☝️';
  };

  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      {/* Instruction text */}
      <Text style={{ fontSize: 18, color: '#666', textAlign: 'center', marginBottom: 30 }}>
        Show your face and hold up:
      </Text>

      {/* Hand gesture and face side by side */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
        gap: 30
      }}>
        {/* Big hand gesture - no border */}
        <View style={{
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Text style={{
            fontSize: size * 0.7,
            textAlign: 'center'
          }}>
            {getFingerEmoji(fingers)}
          </Text>
        </View>

        {/* Face on the right */}
        <View style={{
          alignItems: 'center'
        }}>
          <Text style={{ fontSize: 100 }}>😊</Text>
        </View>
      </View>

      {/* Instruction text */}
      <Text style={{
        fontSize: 24,
        fontWeight: '600',
        color: '#e83333',
        textAlign: 'center',
        marginBottom: 10
      }}>
        {fingers} finger{fingers > 1 ? 's' : ''}
      </Text>

      <Text style={{
        fontSize: 16,
        color: '#666',
        textAlign: 'center'
      }}>
        Make sure both your face and fingers are clearly visible
      </Text>
    </View>
  );
};

// Animated verification instruction component
const VerificationAnimation = ({ requiredFingers, style }) => {
  const themeColors = useThemeColors();
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    // Pulse animation
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: fadeAnim,
          transform: [{ scale: pulseAnim }],
          alignItems: 'center',
          justifyContent: 'center',
        }
      ]}
    >
      <HandGesture fingers={requiredFingers} size={150} color={themeColors.primary} />

      {/* Camera frame overlay with corner indicators */}
      <View style={{
        position: 'absolute',
        width: 200,
        height: 200,
        borderWidth: 2,
        borderColor: '#ff4444',
        borderRadius: 20,
        backgroundColor: 'transparent',
      }}>
        {/* Corner brackets */}
        <View style={{ position: 'absolute', top: -2, left: -2, width: 30, height: 30, borderTopWidth: 4, borderLeftWidth: 4, borderColor: '#ff4444', borderTopLeftRadius: 20 }} />
        <View style={{ position: 'absolute', top: -2, right: -2, width: 30, height: 30, borderTopWidth: 4, borderRightWidth: 4, borderColor: '#ff4444', borderTopRightRadius: 20 }} />
        <View style={{ position: 'absolute', bottom: -2, left: -2, width: 30, height: 30, borderBottomWidth: 4, borderLeftWidth: 4, borderColor: '#ff4444', borderBottomLeftRadius: 20 }} />
        <View style={{ position: 'absolute', bottom: -2, right: -2, width: 30, height: 30, borderBottomWidth: 4, borderRightWidth: 4, borderColor: themeColors.primary, borderBottomRightRadius: 20 }} />
      </View>
    </Animated.View>
  );
};

const ProfileScreen = ({ navigation, route, user, serverAddress, onUpdateProfile, isInitialSetup = false }) => {
  // Force re-render when theme changes
  useThemedStyles();
  // Get current theme colors
  const themeColors = useThemeColors();

  // Loading state
  const [isLoading, setIsLoading] = useState(true);

  // Create dynamic styles that update with theme changes (memoized for performance)
  const dynamicStyles = useMemo(() => ({
    container: {
      flex: 1,
      backgroundColor: themeColors.bgSecondary,
    },

    header: {
      backgroundColor: themeColors.primary,
      paddingTop: 50,
      paddingBottom: 20,
      paddingHorizontal: 20,
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: themeColors.textWhite,
      marginBottom: 10,
    },
    profileSection: {
      backgroundColor: themeColors.bgPrimary,
      borderRadius: 12,
      padding: 20,
      marginHorizontal: 15,
      marginTop: 15,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: themeColors.textPrimary,
      marginBottom: 15,
    },
    inputGroup: {
      marginBottom: 20,
    },
    label: {
      fontSize: 16,
      fontWeight: '500',
      color: themeColors.textPrimary,
      marginBottom: 8,
    },
    input: {
      backgroundColor: themeColors.bgSecondary,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: themeColors.borderSecondary,
      padding: 12,
      fontSize: 16,
      color: themeColors.textPrimary,
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top',
    },
    agePickerButton: {
      backgroundColor: themeColors.bgSecondary,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: themeColors.borderSecondary,
      padding: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    agePickerText: {
      fontSize: 16,
      color: themeColors.textPrimary,
    },
    agePickerPlaceholder: {
      color: themeColors.textSecondary,
    },
    passionContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 10,
    },
    passionChip: {
      backgroundColor: themeColors.primaryLight,
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 16,
      margin: 4,
      borderWidth: 1,
      borderColor: themeColors.primary,
    },
    selectedPassionChip: {
      backgroundColor: themeColors.primary,
    },
    passionText: {
      fontSize: 14,
      fontWeight: '500',
      color: themeColors.primary,
    },
    selectedPassionText: {
      color: themeColors.textWhite,
    },
    passionCounter: {
      marginTop: 10,
      alignItems: 'center',
    },
    passionCounterText: {
      fontSize: 14,
      color: themeColors.textSecondary,
    },
    imageSection: {
      backgroundColor: themeColors.bgPrimary,
      borderRadius: 12,
      padding: 20,
      marginHorizontal: 15,
      marginTop: 15,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    imageGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    imageSlot: {
      width: '48%',
      aspectRatio: 1,
      backgroundColor: themeColors.bgSecondary,
      borderRadius: 12,
      marginBottom: 15,
      borderWidth: 2,
      borderColor: themeColors.borderSecondary,
      borderStyle: 'dashed',
      justifyContent: 'center',
      alignItems: 'center',
    },
    imageSlotFilled: {
      borderStyle: 'solid',
      borderColor: themeColors.primary,
    },
    addImageButton: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    addImageIcon: {
      marginBottom: 8,
    },
    addImageText: {
      fontSize: 14,
      color: themeColors.textSecondary,
      textAlign: 'center',
    },
    profileImage: {
      width: '100%',
      height: '100%',
      borderRadius: 10,
    },
    removeImageButton: {
      position: 'absolute',
      top: 5,
      right: 5,
      backgroundColor: themeColors.textError,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    saveButton: {
      backgroundColor: themeColors.primary,
      paddingVertical: 15,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 15,
    },
    saveButtonText: {
      color: themeColors.textWhite,
      fontSize: 18,
      fontWeight: '600',
    },
    verificationSection: {
      backgroundColor: themeColors.bgPrimary,
      borderRadius: 12,
      padding: 20,
      marginHorizontal: 15,
      marginTop: 15,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    verificationHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    verificationTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: themeColors.textPrimary,
    },
    verificationSubtitle: {
      fontSize: 14,
      color: themeColors.textSecondary,
      marginTop: 4,
    },
    refreshButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: themeColors.bgSecondary,
    },
    verifiedCard: {
      backgroundColor: 'rgba(34, 197, 94, 0.1)',
      borderWidth: 2,
      borderColor: '#22c55e',
      borderRadius: 12,
      padding: 20,
      alignItems: 'center',
    },
    verifiedIcon: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: '#22c55e',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 12,
    },
    verifiedTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#22c55e',
      marginBottom: 8,
    },
    verifiedDate: {
      fontSize: 14,
      color: themeColors.textSecondary,
    },
    badgeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 12,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      borderWidth: 1,
    },
    goldBadgeContainer: {
      backgroundColor: 'rgba(255, 215, 0, 0.1)',
      borderColor: themeColors.gold,
    },
    blueBadgeContainer: {
      backgroundColor: `rgba(${themeColors.primary.replace('#', '').match(/.{2}/g).map(hex => parseInt(hex, 16)).join(', ')}, 0.1)`,
      borderColor: themeColors.primary,
    },
    badgeText: {
      fontSize: 12,
      fontWeight: 'bold',
      marginLeft: 6,
    },
    goldBadgeText: {
      color: themeColors.gold,
    },
    blueBadgeText: {
      color: themeColors.primary,
    },
    pendingCard: {
      backgroundColor: 'rgba(251, 191, 36, 0.1)',
      borderWidth: 2,
      borderColor: '#fbbf24',
      borderRadius: 12,
      padding: 20,
      alignItems: 'center',
    },
    pendingIcon: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: '#fbbf24',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 12,
    },
    pendingTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#fbbf24',
      marginBottom: 8,
    },
    pendingMessage: {
      fontSize: 14,
      color: themeColors.textSecondary,
      textAlign: 'center',
      lineHeight: 20,
    },
    rejectedCard: {
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      borderWidth: 2,
      borderColor: '#ef4444',
      borderRadius: 12,
      padding: 20,
    },
    rejectedHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    rejectedIcon: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: '#ef4444',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    rejectedContent: {
      flex: 1,
    },
    rejectedTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#ef4444',
      marginBottom: 4,
    },
    rejectedReason: {
      fontSize: 14,
      color: themeColors.textSecondary,
      lineHeight: 18,
    },
    retryButton: {
      backgroundColor: themeColors.primary,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 14,
      paddingHorizontal: 24,
      borderRadius: 10,
      marginTop: 16,
    },
    retryButtonText: {
      color: themeColors.textWhite,
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    unverifiedCard: {
      backgroundColor: themeColors.bgSecondary,
      borderWidth: 2,
      borderColor: themeColors.borderSecondary,
      borderRadius: 12,
      padding: 24,
      alignItems: 'center',
    },
    unverifiedIcon: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: themeColors.primaryLight,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    unverifiedTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: themeColors.textPrimary,
      marginBottom: 8,
      textAlign: 'center',
    },
    unverifiedMessage: {
      fontSize: 15,
      color: themeColors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
      marginBottom: 20,
    },
    verifyButton: {
      backgroundColor: themeColors.primary,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 32,
      borderRadius: 12,
      shadowColor: themeColors.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 6,
    },
    verifyButtonDisabled: {
      backgroundColor: themeColors.textLight,
      shadowOpacity: 0,
      elevation: 0,
    },
    verifyButtonText: {
      color: themeColors.textWhite,
      fontSize: 18,
      fontWeight: 'bold',
      marginLeft: 8,
    },
    benefitsContainer: {
      marginTop: 20,
      padding: 16,
      backgroundColor: themeColors.primaryLight,
      borderRadius: 10,
      borderWidth: 1,
      borderColor: themeColors.primary,
    },
    benefitsTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: themeColors.primary,
      marginBottom: 12,
      textAlign: 'center',
    },
    benefitItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    benefitText: {
      fontSize: 14,
      color: themeColors.textPrimary,
      marginLeft: 10,
      flex: 1,
    },
  }), [themeColors]);

  // Simplified state for faster loading
  const [saving, setSaving] = useState(false);
  const [age, setAge] = useState('');
  const [showAgeModal, setShowAgeModal] = useState(false);
  const [description, setDescription] = useState('');
  const [selectedPassions, setSelectedPassions] = useState([]);
  const [images, setImages] = useState(['', '', '', '']);
  const [errors, setErrors] = useState({});

  // Verification-related state
  const [verificationStatus, setVerificationStatus] = useState({
    isVerified: false,
    badgeType: null,
    verifiedAt: null,
    request: null
  });
  const [isLoadingVerification, setIsLoadingVerification] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationPhoto, setVerificationPhoto] = useState(null);
  const [requiredFingers, setRequiredFingers] = useState(null);
  const [showFingerAnimation, setShowFingerAnimation] = useState(false);
  const [showInstructionModal, setShowInstructionModal] = useState(false);
  const [shouldOpenCamera, setShouldOpenCamera] = useState(false);
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Refs for input fields (for focusing next input)
  const descriptionRef = useRef(null);
  const passionsRef = useRef(null);

  // Add effect to handle back button and navigation
  useEffect(() => {
    if (isInitialSetup) {
      // Set custom header options to prevent going back
      navigation.setOptions({
        headerTitle: 'Complete Your Profile',
        headerLeft: () => null,  // Remove back button
        gestureEnabled: false,   // Disable swipe back gesture
      });
    }
  }, [navigation, isInitialSetup]);

  // Fetch user profile data when screen loads
  useEffect(() => {
    const loadProfile = async () => {
      try {
        await fetchProfileData();
      } finally {
        // Set loading to false after profile data is loaded
        setIsLoading(false);
      }
    };
    loadProfile();
  }, []);

  // Request permissions for image library
  useEffect(() => {
    (async () => {
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 
            'Sorry, we need camera roll permissions to upload images.');
        }
      }
    })();
  }, []);

  const fetchProfileData = async () => {
    const userId = getValidUserId();
    if (!userId || !serverAddress) return;

    try {
      // Check if profile is cached first for faster loading
      const [cachedProfile, cacheTimestamp] = await Promise.all([
        AsyncStorage.getItem(`profile_${userId}`),
        AsyncStorage.getItem(`profile_${userId}_timestamp`)
      ]);

      let shouldSkipServer = false;

      if (cachedProfile) {
        const parsedProfile = JSON.parse(cachedProfile);

        // Check if cache is recent (less than 5 minutes old)
        if (cacheTimestamp) {
          const cacheAge = Date.now() - parseInt(cacheTimestamp);
          shouldSkipServer = cacheAge < 5 * 60 * 1000; // 5 minutes
        }

        // Batch state updates to reduce re-renders
        const profileImages = parsedProfile.images?.length > 0
          ? [...parsedProfile.images, ...Array(4 - parsedProfile.images.length).fill('')]
          : ['', '', '', ''];

        // Update all states at once
        setAge(parsedProfile.age?.toString() || '');
        setDescription(parsedProfile.description || '');
        setSelectedPassions(parsedProfile.passions || []);
        setImages(profileImages);

        // If cache is recent, skip server call
        if (shouldSkipServer) return;
      }

      // Fetch from server
      const response = await fetch(`http://${serverAddress}/api/profile/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`Error response from server: ${errorText}`);
        if (response.status === 404) {
          // If user not found, it might be a new user without a profile yet
          // This is not necessarily an error
          return;
        }
        throw new Error(errorText || 'Failed to fetch profile');
      }

      const data = await response.json();

      // Batch state updates for better performance
      const profileImages = data.profile.images && data.profile.images.length > 0
        ? [...data.profile.images, ...Array(4 - data.profile.images.length).fill('')]
        : ['', '', '', ''];

      // Update all states at once to reduce re-renders
      setAge(data.profile.age ? data.profile.age.toString() : '');
      setDescription(data.profile.description || '');
      setSelectedPassions(data.profile.passions || []);
      setImages(profileImages);

      // Cache the profile data with timestamp asynchronously (don't wait for it)
      Promise.all([
        AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(data.profile)),
        AsyncStorage.setItem(`profile_${userId}_timestamp`, Date.now().toString())
      ]).catch(console.error);
    } catch (error) {
      console.error('Error fetching profile:', error);
      // Don't show an alert for 404 errors since this could be a new user
      if (!error.message.includes('not found')) {
        Alert.alert('Error', 'Failed to load profile data. Please try again.');
      }
    }
  };

  const handleImagePick = async (startIndex) => {
    try {
      // Count available empty slots starting from the clicked index
      const emptySlots = [];
      for (let i = startIndex; i < 4; i++) {
        if (images[i] === '') {
          emptySlots.push(i);
        }
      }

      // If no empty slots from this position, just replace the clicked slot
      if (emptySlots.length === 0) {
        emptySlots.push(startIndex);
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        selectionLimit: emptySlots.length, // Limit to available slots
        aspect: [1, 1],
        quality: 0.2,
        base64: true,
        exif: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newImages = [...images];

        // Fill the available slots with selected images
        result.assets.forEach((asset, assetIndex) => {
          if (assetIndex < emptySlots.length) {
            const slotIndex = emptySlots[assetIndex];
            newImages[slotIndex] = asset.base64;
          }
        });

        setImages(newImages);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };



  const handleRemoveImage = (index) => {
    const newImages = [...images];

    // Remove the image at the specified index
    newImages[index] = '';

    // Shift all images forward to fill gaps
    const compactedImages = [];

    // First, collect all non-empty images
    for (let i = 0; i < newImages.length; i++) {
      if (newImages[i] !== '') {
        compactedImages.push(newImages[i]);
      }
    }

    // Fill the array with compacted images and empty slots at the end
    const finalImages = ['', '', '', ''];
    for (let i = 0; i < compactedImages.length; i++) {
      finalImages[i] = compactedImages[i];
    }

    setImages(finalImages);
  };

  // Handle passion selection with constraints
  const togglePassion = (passion) => {
    setSelectedPassions(prev => {
      if (prev.includes(passion)) {
        // Removing a passion - check minimum constraint
        if (prev.length <= 3) {
          Alert.alert(
            "Minimum Required",
            "Please select at least 3 passions to continue.",
            [{ text: "OK" }]
          );
          return prev; // Don't remove if at minimum
        }
        return prev.filter(p => p !== passion);
      } else {
        // Adding a passion - check maximum constraint
        if (prev.length >= 6) {
          Alert.alert(
            "Maximum Reached",
            "You can select up to 6 passions. Please remove one to add another.",
            [{ text: "OK" }]
          );
          return prev; // Don't add if at maximum
        }
        return [...prev, passion];
      }
    });
  };

  // Handle photo reordering
  const movePhoto = (fromIndex, toIndex) => {
    if (toIndex < 0 || toIndex >= 4 || fromIndex === toIndex) return;

    const newImages = [...images];
    const temp = newImages[fromIndex];
    newImages[fromIndex] = newImages[toIndex];
    newImages[toIndex] = temp;
    setImages(newImages);
  };

  // Debug function to validate user ID before using it
  const getValidUserId = () => {
    if (!user) {
      console.log('User object is null or undefined');
      return null;
    }
    
    if (!user.id) {
      console.log('User ID is missing in the user object', user);
      return null;
    }
    
    // Check if the ID looks like a valid MongoDB ObjectId
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(user.id);
    console.log(`User ID: ${user.id}, valid ObjectId format: ${isValidObjectId}`);
    
    return user.id;
  };

  // Add this function to validate the profile
  const validateProfile = () => {
    const newErrors = {};

    if (!description.trim()) {
      newErrors.description = 'Please add a description about yourself';
    }

    if (!age) {
        newErrors.age = 'Age is required';
      } else {
        const ageNum = parseInt(age);
        if (isNaN(ageNum) || ageNum < 18 || ageNum > 120) {
          newErrors.age = 'Please enter a valid age between 18 and 120';
        }
      }

    // Check passion selection constraints
    if (selectedPassions.length < 3) {
      newErrors.passions = `Please select at least 3 passions (${selectedPassions.length}/3 selected)`;
    } else if (selectedPassions.length > 6) {
      newErrors.passions = `Please select no more than 6 passions (${selectedPassions.length}/6 selected)`;
    }

    // Check for minimum of 3 pictures
    if (isInitialSetup) {
      const photoCount = images.filter(img => img !== '').length;
      if (photoCount < 3) {
        newErrors.photos = `Please add at least 3 photos (${photoCount}/3 uploaded)`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveProfile = async () => {
    // Dismiss keyboard
    Keyboard.dismiss();
    
    const userId = getValidUserId();
    if (!userId || !serverAddress) {
      Alert.alert('Error', 'Invalid user ID or server address. Please try logging in again.');
      return;
    }

    // Validate profile data
    if (!validateProfile()) {
      // If there are validation errors and this is initial setup, show an alert
      if (isInitialSetup) {
        Alert.alert(
          'Incomplete Profile',
          'Please complete your profile before continuing. A description about yourself is required.',
          [{ text: 'OK' }]
        );
      }
      return;
    }

    setSaving(true);
    try {
      // Filter out empty images and ensure proper format
      const filteredImages = images
        .filter(img => img !== '')
        .map(img => {
          // If it's already a data URI, extract just the base64 part
          if (img.startsWith('data:image')) {
            return img.split(',')[1];
          }
          // Return as is if it's just the base64 string
          return img;
        });
      
      // Check if images are too large
      const totalImagesSize = JSON.stringify(filteredImages).length;
      if (totalImagesSize > 5000000) { // ~5MB limit
        Alert.alert(
          'Images Too Large', 
          'Your images are too large. Please try using fewer or smaller images.',
          [{ text: 'OK' }]
        );
        setSaving(false);
        return;
      }
      
      const profileData = {
        age: age ? parseInt(age) : null,
        description,
        passions: selectedPassions,
        images: filteredImages,
      };

      console.log(`Saving profile for user ID: ${userId}`);
      
      const response = await fetch(`http://${serverAddress}/api/profile/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`Error response from server: ${errorText}`);
        throw new Error(errorText || 'Failed to update profile');
      }
      
      const data = await response.json().catch(e => {
        console.log('Error parsing JSON response:', e);
        return { profile: profileData };
      });

      // Cache the updated profile data
      await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(data.profile || profileData));
      
      // Update app state if callback provided
      if (onUpdateProfile) {
        onUpdateProfile(data.profile || profileData);
      }

      if (isInitialSetup) {
        // Show alert and then navigate to Home screen
        Alert.alert(
          'Profile Completed',
          'Your profile has been set up. You can now use the app!',
          [
            { 
              text: 'Continue', 
              onPress: () => {
                // Explicitly navigate to Home screen after profile setup
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Home' }],
                });
              }
            }
          ]
        );
      } else {
        Alert.alert('Success', 'Your profile has been updated successfully.');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', `Failed to save profile: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  // ===== VERIFICATION FUNCTIONS =====

  // Load verification status
  const loadVerificationStatus = async () => {
    if (!user || !serverAddress) {
      return;
    }

    setIsLoadingVerification(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }

      if (!token) {
        return;
      }

      const url = `http://${serverAddress}/api/verification/status`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setVerificationStatus(data);
      } else {
        console.log('Verification status error:', response.status);
        setVerificationStatus({
          isVerified: false,
          badgeType: null,
          verifiedAt: null,
          request: null
        });
      }
    } catch (error) {
      console.error('Error loading verification status:', error);
      setVerificationStatus({
        isVerified: false,
        badgeType: null,
        verifiedAt: null,
        request: null
      });
    } finally {
      setIsLoadingVerification(false);
    }
  };

  // Start pulse animation
  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // Direct camera function that bypasses modal issues
  const openCameraDirect = async () => {
    console.log('� openCameraDirect called');
    try {
      // Request permissions first
      console.log('🔐 Requesting camera permissions...');
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      console.log('🔐 Camera permission status:', status);

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is required to take verification photos.');
        return;
      }

      console.log('📸 Opening native camera directly...');

      // Try the most basic camera call possible
      const result = await ImagePicker.launchCameraAsync({
        quality: 0.8,
        base64: true, // Ensure we get base64 data for admin panel
      });

      console.log('📸 Direct camera result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        console.log('✅ Direct camera photo taken successfully');
        const asset = result.assets[0];
        // Use base64 if available, otherwise use URI
        const photoData = asset.base64
          ? `data:image/jpeg;base64,${asset.base64}`
          : asset.uri;
        console.log('📸 Photo data format:', photoData.startsWith('data:') ? 'base64' : 'URI');
        setVerificationPhoto(photoData);
        setShowVerificationModal(true);
      } else {
        console.log('� Camera was canceled or failed');
      }
    } catch (error) {
      console.error('❌ Direct camera error:', error);
      Alert.alert('Camera Error', `Failed to open camera: ${error.message}`);
    }
  };

  // Take verification photo with camera
  const takeVerificationPhoto = async () => {
    console.log('🎯 takeVerificationPhoto called');
    try {
      // Generate random number of fingers (1-5)
      const randomFingers = Math.floor(Math.random() * 5) + 1;
      console.log('🤚 Required fingers:', randomFingers);
      setRequiredFingers(randomFingers);

      // Show instruction modal first
      console.log('📱 Showing instruction modal');
      setShowInstructionModal(true);

    } catch (error) {
      console.error('❌ Error preparing verification photo:', error);
      Alert.alert('Error', 'Failed to prepare camera. Please try again.');
    }
  };



  // Open camera for verification photo
  const openCamera = async () => {
    console.log('🎥 openCamera called');
    try {
      if (Platform.OS === 'web') {
        console.log('📱 Web platform detected');
        // For web browsers, use HTML5 camera input
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.capture = 'environment'; // Use rear camera if available

        input.onchange = (event) => {
          const file = event.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
              const base64Image = e.target.result;
              setVerificationPhoto(base64Image);
              setShowVerificationModal(true);
            };
            reader.readAsDataURL(file);
          }
        };

        input.click();
      } else {
        console.log('📱 Mobile platform detected');
        // For mobile (iOS/Android)
        console.log('🔐 Requesting camera permissions...');
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        console.log('🔐 Camera permission status:', status);

        if (status !== 'granted') {
          console.log('❌ Camera permission denied');
          Alert.alert('Permission Required', 'Sorry, we need camera permissions to take verification photos.');
          return;
        }

        console.log('📸 Launching camera...');
        console.log('📸 ImagePicker object:', ImagePicker);
        console.log('📸 launchCameraAsync function:', typeof ImagePicker.launchCameraAsync);

        // Launch camera with timeout
        console.log('📸 Using image library as workaround for camera issue...');

        // Since launchCameraAsync hangs on iOS, use image library instead
        // This will give user option to take photo or choose from library
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
          base64: true,
        });

        console.log('📸 Image library result received');

        console.log('📸 Camera result received:', result);
        console.log('📸 Result type:', typeof result);
        console.log('📸 Result keys:', Object.keys(result));

        if (!result.canceled && result.assets && result.assets[0]) {
          console.log('✅ Photo taken successfully');
          const asset = result.assets[0];
          const base64Image = `data:image/jpeg;base64,${asset.base64}`;
          setVerificationPhoto(base64Image);
          setShowVerificationModal(true);
        } else {
          console.log('❌ Camera was canceled or no photo taken');
        }
      }
    } catch (error) {
      console.error('❌ Error taking verification photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  // Convert image URI to base64
  const convertImageToBase64 = async (uri) => {
    try {
      if (uri.startsWith('data:')) {
        // Already base64
        console.log('📸 Photo is already base64 format');
        return uri;
      }

      console.log('📸 Converting URI to base64:', uri.substring(0, 50) + '...');

      if (Platform.OS === 'web') {
        // For web, use fetch and FileReader
        const response = await fetch(uri);
        const blob = await response.blob();

        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      } else {
        // For mobile, use expo-file-system if available, or fetch
        try {
          const response = await fetch(uri);
          const blob = await response.blob();

          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
        } catch (fetchError) {
          console.error('Fetch failed, trying alternative method:', fetchError);
          // Fallback: return the URI as is and let the server handle it
          return uri;
        }
      }
    } catch (error) {
      console.error('Error converting image to base64:', error);
      // Fallback: return the original URI
      return uri;
    }
  };

  // Submit verification request
  const submitVerificationRequest = async () => {
    if (!verificationPhoto) {
      Alert.alert('Error', 'Please take a verification photo first.');
      return;
    }

    setIsLoadingVerification(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }

      if (!token) {
        Alert.alert('Error', 'Please log in again.');
        return;
      }

      console.log('📸 Converting verification photo to base64...');
      // Convert photo to base64 for admin panel viewing
      const base64Photo = await convertImageToBase64(verificationPhoto);
      console.log('📸 Photo converted to base64, length:', base64Photo.length);

      const url = `http://${serverAddress}/api/verification/request`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          verificationPhoto: base64Photo,
          requiredFingers: requiredFingers || Math.floor(Math.random() * 5) + 1 // Fallback if not set
        }),
      });

      if (response.ok) {
        const data = await response.json();
        Alert.alert('Success', 'Verification request submitted successfully! We will review it within 24-48 hours.');
        setShowVerificationModal(false);
        setVerificationPhoto(null);
        setRequiredFingers(null);
        loadVerificationStatus(); // Refresh status
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to submit verification request');
      }
    } catch (error) {
      console.error('Error submitting verification request:', error);
      Alert.alert('Error', 'Failed to submit verification request. Please try again.');
    } finally {
      setIsLoadingVerification(false);
    }
  };

  // Render hand gesture based on finger count
  const renderFingerDisplay = (count) => {
    if (!count) return null;

    return (
      <View style={profileStyles.fingerDisplayContainer}>
        <HandGesture fingers={count} size={120} color={themeColors.primary} />
      </View>
    );
  };

  // Load verification status lazily (only when verification section is visible)
  const [verificationLoaded, setVerificationLoaded] = useState(false);

  const loadVerificationIfNeeded = () => {
    if (!verificationLoaded && user && serverAddress) {
      setVerificationLoaded(true);
      loadVerificationStatus();
    }
  };

  // Handle camera opening after modal closes
  useEffect(() => {
    if (shouldOpenCamera && !showInstructionModal) {
      console.log('🎯 Modal closed, opening camera now...');
      setShouldOpenCamera(false);
      // Small delay to ensure UI is stable
      setTimeout(() => {
        openCameraDirect();
      }, 100);
    }
  }, [shouldOpenCamera, showInstructionModal]);

  // Show loading screen while profile is loading
  if (isLoading) {
    return (
      <View style={[dynamicStyles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={themeColors.primary} />
        <Text style={{ marginTop: 16, fontSize: 16, color: themeColors.textSecondary }}>
          Loading...
        </Text>
      </View>
    );
  }

  return (
    <View style={dynamicStyles.container}>
      <ScrollView
        style={dynamicStyles.container}
        keyboardShouldPersistTaps="handled"
        contentInsetAdjustmentBehavior="automatic"
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View>
            {isInitialSetup && (
              <View style={dynamicStyles.profileSection}>
                <Text style={[profileStyles.requiredNote, { color: themeColors.textSecondary }]}>
                  * Description, age, and at least 3 photos are required
                </Text>
              </View>
            )}

          <View style={dynamicStyles.imageSection}>
            <Text style={dynamicStyles.sectionTitle}>Profile Photos</Text>
            <Text style={[profileStyles.sectionSubtitle, { color: themeColors.textSecondary }]}>
              {isInitialSetup
                ? <Text>Add at least 3 photos <Text style={[profileStyles.requiredStar, { color: themeColors.primary }]}>*</Text></Text>
                : 'Add up to 4 photos'}
            </Text>
            {errors.photos && <Text style={[profileStyles.errorText, { color: themeColors.primary }]}>{errors.photos}</Text>}


            <View style={dynamicStyles.imageGrid}>
              {images.map((image, index) => (
                <View key={index} style={[dynamicStyles.imageSlot, image && dynamicStyles.imageSlotFilled]}>
                  {image ? (
                    <View style={profileStyles.imageWrapper}>
                      <Image
                        source={{ uri: image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}` }}
                        style={dynamicStyles.profileImage}
                      />

                      {/* Reorder buttons */}
                      <View style={profileStyles.reorderButtons}>
                        {index > 0 && images[index - 1] && (
                          <TouchableOpacity
                            style={[profileStyles.reorderButton, profileStyles.reorderButtonLeft]}
                            onPress={() => movePhoto(index, index - 1)}
                          >
                            <Ionicons name="chevron-back" size={16} color="#fff" />
                          </TouchableOpacity>
                        )}
                        {index < 3 && images[index + 1] && (
                          <TouchableOpacity
                            style={[profileStyles.reorderButton, profileStyles.reorderButtonRight]}
                            onPress={() => movePhoto(index, index + 1)}
                          >
                            <Ionicons name="chevron-forward" size={16} color="#fff" />
                          </TouchableOpacity>
                        )}
                      </View>

                      <TouchableOpacity
                        style={dynamicStyles.removeImageButton}
                        onPress={() => handleRemoveImage(index)}
                      >
                        <Ionicons name="close-circle" size={24} color={themeColors.textWhite} />
                      </TouchableOpacity>

                      {/* Photo number indicator */}
                      <View style={profileStyles.photoNumber}>
                        <Text style={profileStyles.photoNumberText}>{index + 1}</Text>
                      </View>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={dynamicStyles.addImageButton}
                      onPress={() => handleImagePick(index)}
                    >
                      <Ionicons name="add" size={40} color={themeColors.primary} style={dynamicStyles.addImageIcon} />
                      <Text style={dynamicStyles.addImageText}>Add Photo</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>

          {/* Verification Section */}
          <View style={dynamicStyles.verificationSection}>
            <View style={dynamicStyles.verificationHeader}>
              <View>
                <Text style={dynamicStyles.verificationTitle}>Account Verification</Text>
                <Text style={dynamicStyles.verificationSubtitle}>Get verified to build trust</Text>
              </View>
              <TouchableOpacity
                style={dynamicStyles.refreshButton}
                onPress={loadVerificationStatus}
                disabled={isLoadingVerification}
              >
                <Ionicons
                  name="refresh"
                  size={20}
                  color={isLoadingVerification ? "#ccc" : themeColors.primary}
                />
              </TouchableOpacity>
            </View>

            {verificationStatus.isVerified ? (
              <View style={dynamicStyles.verifiedCard}>
                <View style={dynamicStyles.verifiedIcon}>
                  <Ionicons name="checkmark" size={30} color="#fff" />
                </View>
                <Text style={dynamicStyles.verifiedTitle}>Account Verified</Text>
                <Text style={dynamicStyles.verifiedDate}>
                  Verified on {new Date(verificationStatus.verifiedAt).toLocaleDateString()}
                </Text>
                {verificationStatus.badgeType && (
                  <View style={[
                    dynamicStyles.badgeContainer,
                    verificationStatus.badgeType === 'gold' ? dynamicStyles.goldBadgeContainer : dynamicStyles.blueBadgeContainer
                  ]}>
                    <Ionicons
                      name="star"
                      size={16}
                      color={verificationStatus.badgeType === 'gold' ? themeColors.gold : themeColors.primary}
                    />
                    <Text style={[
                      dynamicStyles.badgeText,
                      verificationStatus.badgeType === 'gold' ? dynamicStyles.goldBadgeText : dynamicStyles.blueBadgeText
                    ]}>
                      {verificationStatus.badgeType === 'gold' ? 'PREMIUM VERIFIED' : 'VERIFIED'}
                    </Text>
                  </View>
                )}
              </View>
            ) : (
              <>
                {verificationStatus.request ? (
                  <>
                    {verificationStatus.request.status === 'rejected' ? (
                      <View style={dynamicStyles.rejectedCard}>
                        <View style={dynamicStyles.rejectedHeader}>
                          <View style={dynamicStyles.rejectedIcon}>
                            <Ionicons name="close" size={24} color="#fff" />
                          </View>
                          <View style={dynamicStyles.rejectedContent}>
                            <Text style={dynamicStyles.rejectedTitle}>Verification Declined</Text>
                            <Text style={dynamicStyles.rejectedReason}>
                              {(() => {
                                const rejectionMessages = {
                                  'photo_unclear': 'Photo quality needs improvement',
                                  'photo_inappropriate': 'Photo content not suitable',
                                  'identity_mismatch': 'Photo doesn\'t match your profile',
                                  'fake_document': 'Document appears altered',
                                  'other': 'Please review requirements'
                                };
                                return rejectionMessages[verificationStatus.request.rejectionReason] || 'Please try again';
                              })()}
                            </Text>
                          </View>
                        </View>
                        <TouchableOpacity
                          style={dynamicStyles.retryButton}
                          onPress={takeVerificationPhoto}
                          disabled={isLoadingVerification}
                        >
                          <Ionicons name="camera" size={20} color={themeColors.textWhite} />
                          <Text style={dynamicStyles.retryButtonText}>Take New Photo</Text>
                        </TouchableOpacity>
                      </View>
                    ) : (
                      <View style={dynamicStyles.pendingCard}>
                        <View style={dynamicStyles.pendingIcon}>
                          <Ionicons name="time" size={30} color="#fff" />
                        </View>
                        <Text style={dynamicStyles.pendingTitle}>Under Review</Text>
                        <Text style={dynamicStyles.pendingMessage}>
                          Your verification request is being reviewed.{'\n'}
                          This usually takes 24-48 hours.
                        </Text>
                      </View>
                    )}
                  </>
                ) : (
                  <View style={dynamicStyles.unverifiedCard}>
                    <View style={dynamicStyles.unverifiedIcon}>
                      <Ionicons name="shield-checkmark" size={40} color={themeColors.primary} />
                    </View>
                    <Text style={dynamicStyles.unverifiedTitle}>Get Verified</Text>
                    <Text style={dynamicStyles.unverifiedMessage}>
                      Verify your account to build trust and get more matches. Show your face and hold up the requested number of fingers.
                    </Text>
                    <TouchableOpacity
                      style={[dynamicStyles.verifyButton, isLoadingVerification && dynamicStyles.verifyButtonDisabled]}
                      onPress={takeVerificationPhoto}
                      disabled={isLoadingVerification}
                    >
                      <Ionicons
                        name={isLoadingVerification ? "hourglass" : "camera"}
                        size={22}
                        color={themeColors.textWhite}
                      />
                      <Text style={dynamicStyles.verifyButtonText}>
                        {isLoadingVerification ? 'Processing...' : 'Start Verification'}
                      </Text>
                    </TouchableOpacity>


                  </View>
                )}
              </>
            )}
          </View>



          <View style={dynamicStyles.profileSection}>
            <Text style={dynamicStyles.sectionTitle}>About You</Text>

            <View style={dynamicStyles.inputGroup}>
              <Text style={dynamicStyles.label}>
                Age {<Text style={[profileStyles.requiredStar, { color: themeColors.primary }]}>*</Text>}
              </Text>
              <TouchableOpacity
                style={[
                  dynamicStyles.agePickerButton,
                  errors.age && { borderColor: themeColors.primary }
                ]}
                onPress={() => setShowAgeModal(true)}
              >
                <Text style={[
                  dynamicStyles.agePickerText,
                  !age && dynamicStyles.agePickerPlaceholder
                ]}>
                  {age || 'Select your age'}
                </Text>
                <Ionicons name="chevron-down" size={20} color={themeColors.textSecondary} />
              </TouchableOpacity>
              {errors.age && <Text style={[profileStyles.errorText, { color: themeColors.primary }]}>{errors.age}</Text>}
            </View>

            <View style={dynamicStyles.inputGroup}>
              <Text style={dynamicStyles.label}>
                Description <Text style={[profileStyles.requiredStar, { color: themeColors.primary }]}>*</Text>
              </Text>
              <TextInput
                ref={descriptionRef}
                style={[
                  dynamicStyles.input,
                  dynamicStyles.textArea,
                  errors.description && { borderColor: themeColors.primary }
                ]}
                placeholder="Share a little about yourself..."
                placeholderTextColor={themeColors.textSecondary}
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={4}
                maxLength={500}
                returnKeyType="next"
                onSubmitEditing={() => passionsRef.current && passionsRef.current.focus()}
                autoCorrect={false}
                autoCompleteType="off"
                textContentType="none"
                spellCheck={false}
              />
              <Text style={[profileStyles.charCount, { color: themeColors.textSecondary }]}>{description.length}/500</Text>
              {errors.description && <Text style={[profileStyles.errorText, { color: themeColors.primary }]}>{errors.description}</Text>}
            </View>

            <View style={dynamicStyles.inputGroup}>
              <Text style={dynamicStyles.label}>
                Passions <Text style={[profileStyles.requiredStar, { color: themeColors.primary }]}>*</Text>
              </Text>
              <Text style={[profileStyles.helperText, { color: themeColors.textSecondary }]}>
                Select 3-6 interests that represent you.
              </Text>
              {errors.passions && <Text style={[profileStyles.errorText, { color: themeColors.primary }]}>{errors.passions}</Text>}

              <View style={dynamicStyles.passionContainer}>
                {PASSION_OPTIONS.map((passion) => (
                  <TouchableOpacity
                    key={passion}
                    style={[
                      dynamicStyles.passionChip,
                      selectedPassions.includes(passion) && dynamicStyles.selectedPassionChip,
                      selectedPassions.length >= 6 && !selectedPassions.includes(passion) && profileStyles.disabledPassionTag
                    ]}
                    onPress={() => togglePassion(passion)}
                    disabled={selectedPassions.length >= 6 && !selectedPassions.includes(passion)}
                  >
                    <Text style={[
                      dynamicStyles.passionText,
                      selectedPassions.includes(passion) && dynamicStyles.selectedPassionText,
                      selectedPassions.length >= 6 && !selectedPassions.includes(passion) && profileStyles.disabledPassionTagText
                    ]}>
                      {passion}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={dynamicStyles.passionCounter}>
                <Text style={[
                  dynamicStyles.passionCounterText,
                  selectedPassions.length < 3 && profileStyles.warningText,
                  selectedPassions.length >= 3 && selectedPassions.length <= 6 && profileStyles.successText
                ]}>
                  {selectedPassions.length}/6 passions selected
                  {selectedPassions.length < 3 && ` (need ${3 - selectedPassions.length} more)`}
                </Text>
              </View>
            </View>
          </View>

          <View style={profileStyles.buttonContainer}>
            <TouchableOpacity
              style={dynamicStyles.saveButton}
              onPress={handleSaveProfile}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator color={themeColors.textWhite} size="small" />
              ) : (
                <Text style={dynamicStyles.saveButtonText}>
                  {isInitialSetup ? 'Complete Profile' : 'Save Profile'}
                </Text>
              )}
            </TouchableOpacity>
          </View>

            {/* Bottom padding to ensure scrollability */}
            <View style={{ height: 50 }} />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>

      {/* Age Selection Modal */}
      <Modal
        visible={showAgeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAgeModal(false)}
      >
        <View style={profileStyles.modalOverlay}>
          <View style={[profileStyles.modalContent, { backgroundColor: themeColors.bgPrimary }]}>
            <View style={profileStyles.modalHeader}>
              <Text style={[profileStyles.modalTitle, { color: themeColors.textPrimary }]}>Select Your Age</Text>
              <TouchableOpacity
                onPress={() => setShowAgeModal(false)}
                style={profileStyles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color={themeColors.textSecondary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={profileStyles.ageList} showsVerticalScrollIndicator={false}>
              {Array.from({ length: 103 }, (_, i) => i + 18).map((ageOption) => (
                <TouchableOpacity
                  key={ageOption}
                  style={[
                    profileStyles.ageOption,
                    age === ageOption.toString() && profileStyles.selectedAgeOption
                  ]}
                  onPress={() => {
                    setAge(ageOption.toString());
                    setShowAgeModal(false);
                  }}
                >
                  <Text style={[
                    profileStyles.ageOptionText,
                    age === ageOption.toString() && profileStyles.selectedAgeOptionText
                  ]}>
                    {ageOption}
                  </Text>
                  {age === ageOption.toString() && (
                    <Ionicons name="checkmark" size={20} color={themeColors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Instruction Modal */}
      <Modal
        visible={showInstructionModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowInstructionModal(false)}
      >
        <View style={profileStyles.instructionModalOverlay}>
          <View style={profileStyles.instructionModalContent}>
            <View style={profileStyles.instructionModalHeader}>
              <Text style={profileStyles.instructionModalTitle}>Verification Photo</Text>
              <TouchableOpacity
                onPress={() => setShowInstructionModal(false)}
                style={profileStyles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <View style={profileStyles.instructionContent}>
              <InstructionDisplay fingers={requiredFingers} size={200} />
            </View>

            <View style={profileStyles.instructionButtons}>
              <TouchableOpacity
                style={profileStyles.cancelButton}
                onPress={() => setShowInstructionModal(false)}
              >
                <Text style={profileStyles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={profileStyles.takePhotoButton}
                onPress={() => {
                  console.log('🎯 Take Photo button pressed');
                  console.log('🎯 Closing modal and using Alert approach...');
                  setShowInstructionModal(false);

                  // Use Alert approach which might work better
                  setTimeout(() => {
                    Alert.alert(
                      'Take Verification Photo',
                      `Hold up ${requiredFingers} finger${requiredFingers > 1 ? 's' : ''} and take a selfie`,
                      [
                        { text: 'Cancel', style: 'cancel' },
                        {
                          text: 'Open Camera',
                          onPress: async () => {
                            try {
                              console.log('📸 Alert camera button pressed');

                              const { status } = await ImagePicker.requestCameraPermissionsAsync();
                              if (status !== 'granted') {
                                Alert.alert('Permission Required', 'Camera permission is required');
                                return;
                              }

                              console.log('📸 Launching camera from Alert...');
                              const result = await ImagePicker.launchCameraAsync({
                                quality: 0.8,
                                base64: true, // Ensure we get base64 data
                              });

                              console.log('📸 Alert camera result:', result);

                              if (!result.canceled && result.assets && result.assets[0]) {
                                console.log('✅ Alert camera success');
                                const asset = result.assets[0];
                                // Use base64 if available, otherwise use URI
                                const photoData = asset.base64
                                  ? `data:image/jpeg;base64,${asset.base64}`
                                  : asset.uri;
                                console.log('📸 Photo data format:', photoData.startsWith('data:') ? 'base64' : 'URI');
                                setVerificationPhoto(photoData);
                                setShowVerificationModal(true);
                              }
                            } catch (error) {
                              console.error('❌ Alert camera error:', error);
                              Alert.alert('Error', `Camera failed: ${error.message}`);
                            }
                          }
                        }
                      ]
                    );
                  }, 300);
                }}
              >
                <Ionicons name="camera" size={20} color="#fff" style={{ marginRight: 8 }} />
                <Text style={profileStyles.takePhotoButtonText}>Take Photo</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Finger Animation Modal */}
      <Modal
        visible={showFingerAnimation}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowFingerAnimation(false)}
      >
        <View style={profileStyles.fingerAnimationOverlay}>
          <View style={profileStyles.fingerAnimationContainer}>
            <Animated.View style={[profileStyles.fingerDisplay, { transform: [{ scale: pulseAnim }] }]}>
              {renderFingerDisplay(requiredFingers)}
            </Animated.View>
            <View style={profileStyles.cameraIcon}>
              <Ionicons name="camera" size={40} color={themeColors.textError} />
            </View>
          </View>
        </View>
      </Modal>

      {/* Verification Photo Modal */}
      <Modal
        visible={showVerificationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowVerificationModal(false)}
      >
        <View style={profileStyles.verificationModalContainer}>
          <View style={profileStyles.verificationModalHeader}>
            <TouchableOpacity
              style={profileStyles.modalCloseButton}
              onPress={() => setShowVerificationModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            <View style={profileStyles.headerCenter}>
              <Ionicons name="shield-checkmark" size={24} color={themeColors.primary} />
            </View>
            <TouchableOpacity
              style={[profileStyles.submitButton, { opacity: verificationPhoto ? 1 : 0.5 }]}
              onPress={submitVerificationRequest}
              disabled={!verificationPhoto || isLoadingVerification}
            >
              <Ionicons
                name={isLoadingVerification ? "hourglass" : "checkmark"}
                size={16}
                color="#fff"
              />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={profileStyles.verificationModalContent}
            contentContainerStyle={profileStyles.verificationModalScrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Visual finger requirement display */}
            <View style={profileStyles.fingerRequirementDisplay}>
          
              {requiredFingers && (
                <Text style={profileStyles.fingerInstructionText}>
                  {(() => {
                    const fingerEmojis = { 1: '☝️', 2: '✌️', 3: '🤟', 4: '🖖', 5: '🖐️' };
                    return fingerEmojis[requiredFingers] || '☝️';
                  })()} Hold up {requiredFingers} finger{requiredFingers > 1 ? 's' : ''}
                </Text>
              )}
            </View>

            {verificationPhoto && (
              <View style={profileStyles.photoPreviewContainer}>
                <Image source={{ uri: verificationPhoto }} style={profileStyles.photoPreview} />

              </View>
            )}

            {/* Tips with icons and text */}
            <View style={profileStyles.verificationTips}>
              <Text style={profileStyles.tipsTitle}>Tips for verification:</Text>
              <View style={profileStyles.tipRow}>
                <Ionicons name="sunny" size={16} color="#FFD700" />
                <Text style={profileStyles.tipText}>Good lighting</Text>
              </View>
              <View style={profileStyles.tipRow}>
                <Ionicons name="eye" size={16} color={themeColors.primary} />
                <Text style={profileStyles.tipText}>Face clearly visible</Text>
              </View>
              <View style={profileStyles.tipRow}>
                <Ionicons name="checkmark-circle" size={16} color={themeColors.textSuccess} />
                <Text style={profileStyles.tipText}>Hold steady</Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

// Styles are now imported from ScreensStyles.js

export default ProfileScreen;

