// screens/ChatScreen.js
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Keyboard,
  Dimensions, // Add Dimensions import
  Image,
  TouchableWithoutFeedback
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { chatStyles, theme } from './ScreensStyles';
import { useThemedStyles, useThemeColors } from '../contexts/ThemeContext';
import { useHeaderHeight } from '@react-navigation/elements';

const ChatScreen = ({ route, navigation, socket, user, onDeleteChat, onBlockUser }) => {
  // Force re-render when theme changes
  useThemedStyles();
  // Get current theme colors
  const themeColors = useThemeColors();

  // Create dynamic styles that update with theme changes
  const dynamicStyles = {
    container: {
      flex: 1,
      backgroundColor: themeColors.bgSecondary,
      paddingBottom: Platform.OS === 'ios' ? 20 : 0,
    },
    headerButton: {
      padding: 10,
    },
    profileButton: {
      marginRight: 15,
      padding: 5,
    },
    profileContainer: {
      position: 'relative',
    },
    profileImage: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: themeColors.bgSecondary,
      borderWidth: 2,
      borderColor: themeColors.textWhite,
    },
    defaultAvatar: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: themeColors.primary,
    },
    verificationIndicator: {
      position: 'absolute',
      bottom: -2,
      right: -2,
      width: 18,
      height: 18,
      borderRadius: 9,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: themeColors.textWhite,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 3,
    },
    goldIndicator: {
      backgroundColor: themeColors.gold,
    },
    blueIndicator: {
      backgroundColor: themeColors.primary,
    },
    messagesContainer: {
      flex: 1,
      paddingHorizontal: 16,
      paddingTop: 10,
      backgroundColor: themeColors.bgSecondary,
    },
    messagesList: {
      paddingBottom: 20,
    },
    messageContainer: {
      marginVertical: 3,
      maxWidth: '85%',
    },
    sentMessage: {
      alignSelf: 'flex-end',
    },
    receivedMessage: {
      alignSelf: 'flex-start',
    },
    messageBubble: {
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 20,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    sentBubble: {
      backgroundColor: themeColors.primary,
      borderBottomRightRadius: 6,
    },
    receivedBubble: {
      backgroundColor: themeColors.bgPrimary,
      borderBottomLeftRadius: 6,
      borderWidth: 1,
      borderColor: themeColors.borderPrimary,
    },
    messageText: {
      fontSize: 16,
      lineHeight: 20,
    },
    sentText: {
      color: themeColors.textWhite,
    },
    receivedText: {
      color: themeColors.textPrimary,
    },
    messageTime: {
      fontSize: 12,
      marginTop: 4,
      alignSelf: 'flex-end',
    },
    sentTime: {
      color: 'rgba(255, 255, 255, 0.7)',
    },
    receivedTime: {
      color: themeColors.textSecondary,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: themeColors.bgPrimary,
      borderTopWidth: 1,
      borderTopColor: themeColors.borderPrimary,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 5,
    },
    textInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: themeColors.borderSecondary,
      borderRadius: 22,
      paddingHorizontal: 16,
      paddingVertical: 12,
      marginRight: 12,
      maxHeight: 100,
      fontSize: 16,
      backgroundColor: themeColors.bgSecondary,
      color: themeColors.textPrimary,
    },
    sendButton: {
      backgroundColor: themeColors.primary,
      borderRadius: 22,
      width: 44,
      height: 44,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: themeColors.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 3,
    },
    sendButtonDisabled: {
      backgroundColor: themeColors.textLight,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 10,
      fontSize: 16,
      color: themeColors.textSecondary,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorText: {
      fontSize: 16,
      color: themeColors.textError,
      textAlign: 'center',
      marginBottom: 20,
    },
    retryButton: {
      backgroundColor: themeColors.primary,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
    },
    retryButtonText: {
      color: themeColors.textWhite,
      fontSize: 16,
      fontWeight: '600',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyText: {
      fontSize: 18,
      color: themeColors.textSecondary,
      textAlign: 'center',
      marginBottom: 10,
    },
    emptySubtext: {
      fontSize: 14,
      color: themeColors.textLight,
      textAlign: 'center',
    },
    backButton: {
      backgroundColor: themeColors.primary,
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 8,
      marginTop: 15,
    },
    backButtonText: {
      color: themeColors.textWhite,
      fontSize: 16,
      fontWeight: '600',
    },
    blockedBanner: {
      backgroundColor: themeColors.textError,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 10,
      paddingHorizontal: 15,
    },
    blockedText: {
      color: themeColors.textWhite,
      fontSize: 14,
      fontWeight: '600',
      marginLeft: 8,
    },
    disabledInput: {
      backgroundColor: themeColors.bgTertiary,
      color: themeColors.textLight,
    },
  };

  // Handle case where route.params might be undefined
  const match = route?.params?.match || {};
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(true);
  const flatListRef = useRef(null);
  const [isBlocked, setIsBlocked] = useState(false);
  const headerHeight = useHeaderHeight();

  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [bottomPadding, setBottomPadding] = useState(0);
  const [matchProfile, setMatchProfile] = useState(null);

  // Calculate bottom padding for different device types
  useEffect(() => {
    const calculateBottomPadding = () => {
      const screenHeight = Dimensions.get('window').height;
      const windowHeight = Dimensions.get('screen').height;
      
      // Difference represents bottom navigation/gesture area
      const bottomNavHeight = screenHeight - windowHeight;
      
      // Add some extra padding for safety
      setBottomPadding(Math.max(bottomNavHeight, 20));
    };

    // Calculate on mount and when dimensions change
    calculateBottomPadding();
    const dimensionsHandler = Dimensions.addEventListener('change', calculateBottomPadding);

    return () => {
      // Clean up listener
      dimensionsHandler.remove();
    };
  }, []);

  // Add keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (e) => {
        // Update keyboard height
        setKeyboardHeight(e.endCoordinates.height);
  
        // Scroll to the last message after a short delay
        setTimeout(() => {
          if (flatListRef.current && messages.length > 0) {
            flatListRef.current.scrollToEnd({ animated: true });
          }
        }, 100); // Small delay to ensure the layout is updated
      }
    );
  
    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      }
    );
  
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [messages.length]);
  
  // Scroll when keyboard height changes
  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
    }, [keyboardHeight, messages.length]);
  // Handle view profile button press
  const handleViewProfile = () => {
    if (!match?.userId || !match?.username) {
      Alert.alert("Error", "Cannot view profile. Missing user information.");
      return;
    }

    navigation.navigate('ViewProfile', {
      userId: match.userId,
      username: match.username,
      forceRefresh: true // Always force refresh when viewing from chat
    });
  };

  // Set the title to the match's username and add profile picture
  useEffect(() => {
    navigation.setOptions({
      title: match?.username || "Chat",
      headerBackTitle: "Shake", // This sets what the back button will show on the next screen
      headerRight: () => (
        <TouchableOpacity
          style={dynamicStyles.profileButton}
          onPress={handleViewProfile}
        >
          <View style={dynamicStyles.profileContainer}>
            {matchProfile && matchProfile.images && matchProfile.images.length > 0 ? (
              <Image
                source={{
                  uri: matchProfile.images[0].startsWith('data:')
                    ? matchProfile.images[0]
                    : `data:image/jpeg;base64,${matchProfile.images[0]}`
                }}
                style={dynamicStyles.profileImage}
              />
            ) : (
              <View style={[dynamicStyles.profileImage, dynamicStyles.defaultAvatar]}>
                <Ionicons name="person" size={20} color={themeColors.textSecondary} />
              </View>
            )}
            {matchProfile?.verification?.isVerified && (
              <View style={[
                dynamicStyles.verificationIndicator,
                matchProfile.badgeType === 'gold' ? dynamicStyles.goldIndicator : dynamicStyles.blueIndicator
              ]}>
                <Ionicons
                  name="checkmark-circle"
                  size={12}
                  color={themeColors.textWhite}
                />
              </View>
            )}
          </View>
        </TouchableOpacity>
      ),
    });
  }, [navigation, match, matchProfile, themeColors]);

  // Fetch match profile data for the profile picture
  useEffect(() => {
    const fetchMatchProfile = async () => {
      if (!match?.userId) return;

      try {
        // Check for cached profile first
        const cachedProfile = await AsyncStorage.getItem(`profile_${match.userId}`);
        if (cachedProfile) {
          setMatchProfile(JSON.parse(cachedProfile));
        }

        // Try to fetch fresh data from server
        const serverAddress = await AsyncStorage.getItem('serverAddress');
        if (serverAddress) {
          const response = await fetch(`http://${serverAddress}/api/profile/${match.userId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const data = await response.json();
            setMatchProfile(data.profile);

            // Cache the profile data
            await AsyncStorage.setItem(`profile_${match.userId}`, JSON.stringify(data.profile));
          }
        }
      } catch (error) {
        console.error('Error fetching match profile:', error);
      }
    };

    fetchMatchProfile();
  }, [match?.userId]);

  // Check if this user has been blocked when entering the chat
useEffect(() => {
  const checkBlockStatus = async () => {
    try {
      if (!socket || !socket.connected || !user?.id || !match?.userId) return;
      
      // Request block status from server
      socket.emit('checkBlockStatus', {
        userId: user.id,
        otherUserId: match.userId
      });
      
      // Set up listener for the response
      const handleBlockStatus = (data) => {
        if (data.isBlocked) {
          setIsBlocked(true);
        }
      };
      
      socket.on('blockStatus', handleBlockStatus);
      
      return () => {
        socket.off('blockStatus', handleBlockStatus);
      };
    } catch (error) {
      console.error('Error checking block status:', error);
    }
  };
  
  checkBlockStatus();
}, [socket, user?.id, match?.userId]);

  // Mark messages as read when entering the screen
  useEffect(() => {
    const markMessagesAsRead = async () => {
      try {
        if (!user?.id || !match?.userId) return;
        
        // Update matches in AsyncStorage to remove unread badge
        const storedMatchesJSON = await AsyncStorage.getItem(`matches_${user.id}`);
        if (storedMatchesJSON) {
          const storedMatches = JSON.parse(storedMatchesJSON);
          const updatedMatches = storedMatches.map(m => {
            if (m.userId === match.userId) {
              return { 
                ...m, 
                hasUnreadMessages: false,
                hasActivity: true, // Mark as active since user opened the chat
                lastActivity: new Date().toISOString()
              };
            }
            return m;
          });
          
          // Save updated matches back to AsyncStorage
          await AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));
        }
        
        // Send read receipt to server
        if (socket && socket.connected) {
          socket.emit('markMessagesAsRead', {
            userId: user.id,
            otherUserId: match.userId
          });
        }
      } catch (error) {
        console.error('Error marking messages as read:', error);
      }
    };
    
    markMessagesAsRead();
  }, [user?.id, match?.userId, socket]);

  // Load previous messages from storage and sync with server
  useEffect(() => {
    const loadMessages = async () => {
      try {
        if (!user?.id || !match?.userId) {
          setLoading(false);
          return;
        }

        const chatId = getChatId(user.id, match.userId);
        const storedMessages = await AsyncStorage.getItem(`chat_${chatId}`);
        
        if (storedMessages) {
          setMessages(JSON.parse(storedMessages));
        }
        
        setLoading(false);
        
        // Sync with server to get any missed messages
        syncMessagesWithServer();
      } catch (error) {
        console.error('Error loading messages:', error);
        setLoading(false);
      }
    };
    
    // Sync messages with server
    const syncMessagesWithServer = () => {
      if (socket && socket.connected && user?.id && match?.userId) {
        // Request any unread messages from the server
        socket.emit('getUnreadMessages', {
          userId: user.id,
          otherUserId: match.userId
        });
      }
    };
    
    loadMessages();
    
    // Set up a timer to periodically sync messages
    const syncInterval = setInterval(() => {
      if (socket && socket.connected) {
        syncMessagesWithServer();
      }
    }, 30000); // Sync every 30 seconds
    
    return () => {
      clearInterval(syncInterval);
    };
  }, [user?.id, match?.userId, socket]);


  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current.scrollToEnd({ animated: false });
      }, 100); // Small delay to ensure the layout is ready
    }
  }, [messages.length]); // Trigger when messages change
  // Listen for incoming messages

    useEffect(() => {
      if (socket && user?.id && match?.userId) {
        const handleMessage = (data) => {
          if (
            (data.senderId === match.userId && data.receiverId === user.id) ||
            (data.senderId === user.id && data.receiverId === match.userId)
          ) {
            const newMessage = {
              id: data.id,
              text: data.text,
              senderId: data.senderId,
              timestamp: data.timestamp,
            };
    
            setMessages((prevMessages) => {
              // Check if message already exists to avoid duplicates
              const messageExists = prevMessages.some(msg => msg.id === newMessage.id);
              if (messageExists) {
                return prevMessages;
              }
    
              const updatedMessages = [...prevMessages, newMessage];
    
              // Save updated messages to AsyncStorage
              saveMessages(updatedMessages);
    
              // Scroll to end after the state is updated
              setTimeout(() => {
                if (flatListRef.current) {
                  flatListRef.current.scrollToEnd({ animated: true });
                }
              }, 100); // Small delay to ensure the state is updated
    
              return updatedMessages;
            });
    
            // Send read receipt for messages from the other user
            if (data.senderId === match.userId) {
              socket.emit('messageRead', {
                messageId: data.id,
                userId: user.id,
                otherUserId: match.userId
              });
            }
          }
        };
    
        socket.on('message', handleMessage);
    
        return () => {
          socket.off('message', handleMessage);
        };
      }
    }, [socket, user?.id, match?.userId]);
  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0 && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Helper function to create a unique chat ID
  const getChatId = (userId1, userId2) => {
    if (!userId1 || !userId2) return 'invalid_chat';
    return [userId1, userId2].sort().join('_');
  };

  // Save messages to AsyncStorage
  const saveMessages = async (messagesToSave) => {
    try {
      if (!user?.id || !match?.userId) return;
      
      const chatId = getChatId(user.id, match.userId);
      await AsyncStorage.setItem(`chat_${chatId}`, JSON.stringify(messagesToSave));
      
      // Also update the match to have activity so it doesn't auto-delete
      const storedMatchesJSON = await AsyncStorage.getItem(`matches_${user.id}`);
      if (storedMatchesJSON) {
        const storedMatches = JSON.parse(storedMatchesJSON);
        const updatedMatches = storedMatches.map(m => {
          if (m.userId === match.userId) {
            return { 
              ...m, 
              hasActivity: true,
              lastActivity: new Date().toISOString()
            };
          }
          return m;
        });
        
        // Save updated matches back to AsyncStorage
        await AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));
      }
    } catch (error) {
      console.error('Error saving messages:', error);
    }
  };

  // Send a message
  const sendMessage = () => {
    if (!inputText.trim() || !socket || !user?.id || !match?.userId) return;
  
    const newMessage = {
      id: Date.now().toString(), // Simple ID generation
      text: inputText.trim(),
      senderId: user.id,
      timestamp: new Date().toISOString(),
    };
  
    // Emit the message to the server
    socket.emit('sendMessage', {
      id: newMessage.id,
      senderId: user.id,
      senderUsername: user.username,
      receiverId: match.userId,
      receiverUsername: match.username,
      text: newMessage.text,
      timestamp: newMessage.timestamp,
    });
  
    // Add to local state and scroll to end after the state is updated
    setMessages((prevMessages) => {
      const updatedMessages = [...prevMessages, newMessage];
  
      // Save to AsyncStorage
      saveMessages(updatedMessages);
  
      // Scroll to end after the state is updated
      setTimeout(() => {
        if (flatListRef.current) {
          flatListRef.current.scrollToEnd({ animated: true });
        }
      }, 100); // Small delay to ensure the state is updated
  
      return updatedMessages;
    });
  
    // Clear input
    setInputText('');
  };
  // Format timestamp to readable format
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      return '';
    }
  };

  // Render message item
  const renderMessage = ({ item }) => {
    if (!user?.id) return null;

    const isOwnMessage = item.senderId === user.id;

    return (
      <View
        style={[
          dynamicStyles.messageContainer,
          isOwnMessage ? dynamicStyles.sentMessage : dynamicStyles.receivedMessage,
        ]}
      >
        <View
          style={[
            dynamicStyles.messageBubble,
            isOwnMessage ? dynamicStyles.sentBubble : dynamicStyles.receivedBubble,
          ]}
        >
          <Text style={[
            dynamicStyles.messageText,
            isOwnMessage ? dynamicStyles.sentText : dynamicStyles.receivedText,
          ]}>{item.text}</Text>
          <Text
            style={[
              dynamicStyles.messageTime,
              isOwnMessage ? dynamicStyles.sentTime : dynamicStyles.receivedTime,
            ]}
          >
            {formatTime(item.timestamp)}
          </Text>
        </View>
      </View>
    );
  };

  // Handle error case where data is missing
  if (!match?.userId || !user?.id) {
    return (
      <View style={dynamicStyles.errorContainer}>
        <Text style={dynamicStyles.errorText}>Cannot load chat. Missing user information.</Text>
        <TouchableOpacity
          style={dynamicStyles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={dynamicStyles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }


    return (
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          style={dynamicStyles.container}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={headerHeight} // Use actual header height
        >
        {loading ? (
          <View style={dynamicStyles.loadingContainer}>
            <ActivityIndicator size="large" color={themeColors.primary} />
            <Text style={dynamicStyles.loadingText}>Loading messages...</Text>
          </View>
        ) : (
          <>
            <View style={dynamicStyles.messagesContainer}>
              <FlatList
                ref={flatListRef}
                data={messages}
                keyExtractor={(item) => item.id}
                renderItem={renderMessage}
                contentContainerStyle={[
                  dynamicStyles.messagesList,
                  {
                    paddingBottom: keyboardHeight + 120 + bottomPadding, // Increased padding for bottom overlay
                  },
                ]}
              onContentSizeChange={() => {
                if (messages.length > 0 && flatListRef.current) {
                  flatListRef.current.scrollToEnd({ animated: true });
                }
              }}
              onLayout={() => {
                if (messages.length > 0 && flatListRef.current) {
                  flatListRef.current.scrollToEnd({ animated: false });
                }
              }}
              keyboardDismissMode="interactive"
              keyboardShouldPersistTaps="handled"
              />
            </View>

            {isBlocked && (
              <View style={dynamicStyles.blockedBanner}>
                <Ionicons name="ban" size={20} color={themeColors.textWhite} />
                <Text style={dynamicStyles.blockedText}>
                  You have been blocked by this user
                </Text>
              </View>
            )}

            <View style={[
              dynamicStyles.inputContainer,
              { paddingBottom: Platform.OS === 'ios' ? 20 : bottomPadding + 10 }
            ]}>
              <TextInput
                style={[
                  dynamicStyles.textInput,
                  isBlocked && dynamicStyles.disabledInput
                ]}
                value={inputText}
                onChangeText={setInputText}
                placeholder={isBlocked ? "Can't send messages" : "Type a message..."}
                placeholderTextColor={themeColors.textSecondary}
                multiline
                maxHeight={100}
                editable={!isBlocked}
              />
              <TouchableOpacity
                style={[
                  dynamicStyles.sendButton,
                  (!inputText.trim() || isBlocked) && dynamicStyles.sendButtonDisabled
                ]}
                onPress={sendMessage}
                disabled={!inputText.trim() || isBlocked}
              >
                <Ionicons
                  name="send"
                  size={24}
                  color={themeColors.textWhite}
                />
              </TouchableOpacity>
            </View>
          </>
        )}
      </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    );
  };

// Styles are now imported from ScreensStyles.js

export default ChatScreen;

