// screens/RegisterScreen.js - Modern design with fixed keyboard issues
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Image,
  Platform,
  Keyboard,
  Dimensions,
  Animated,
  TouchableWithoutFeedback
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { registerStyles, theme } from './ScreensStyles';
import { useThemeColors } from '../contexts/ThemeContext';

const RegisterScreen = ({ navigation, serverAddress: propServerAddress }) => {
  // Get current theme colors
  const themeColors = useThemeColors();

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [serverAddress, setServerAddress] = useState(propServerAddress || '**************:3000');
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [screenHeight, setScreenHeight] = useState(Dimensions.get('window').height);
  
  // Animation values
  const logoPosition = useRef(new Animated.Value(0)).current;
  const formOpacity = useRef(new Animated.Value(1)).current;
  
  // Reference for text inputs
  const passwordInputRef = useRef(null);
  const confirmPasswordInputRef = useRef(null);

  // Handle keyboard appearance and animations
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setKeyboardVisible(true);
        // Calculate how much we need to move the form up to avoid keyboard
        const keyboardHeight = event.endCoordinates.height;
        
        // On iOS we need to consider the keyboard takes up more space
        const formHeight = 350; // Estimated form height (larger for register)
        const contentHeight = 500; // Estimated total content height
        const availableHeight = screenHeight - keyboardHeight;
        
        // Calculate how much we need to move to ensure fields are visible
        // Using your adjustment of +200 instead of +100
        const moveUpValue = Platform.OS === 'ios' 
          ? -Math.min(contentHeight - availableHeight + 200, 250) 
          : -Math.min(keyboardHeight * 0.5, 150);
        
        Animated.parallel([
          Animated.timing(logoPosition, {
            toValue: moveUpValue,
            duration: 300,
            useNativeDriver: true
          }),
          Animated.timing(formOpacity, {
            toValue: 0.97,
            duration: 300,
            useNativeDriver: true
          })
        ]).start();
      }
    );
    
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        Animated.parallel([
          Animated.timing(logoPosition, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true
          }),
          Animated.timing(formOpacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true
          })
        ]).start();
      }
    );

    // Screen dimensions listener for orientation changes
    const dimensionListener = Dimensions.addEventListener(
      'change',
      ({ window }) => {
        setScreenHeight(window.height);
      }
    );

    // Cleanup
    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
      dimensionListener.remove();
    };
  }, []);

  const handleRegister = async () => {
    Keyboard.dismiss();
    
    // Basic validation
    if (!username.trim() || !password.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    setIsLoading(true);

    try {
      // Save server address for login screen to use
      await AsyncStorage.setItem('serverAddress', serverAddress);

      // Connect to server
      const response = await fetch(`http://${serverAddress}/api/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      // Registration successful
      Alert.alert(
        'Registration Successful',
        'Your account has been created. You can now log in.',
        [{ text: 'OK', onPress: () => navigation.replace('Login') }]
      );
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert(
        'Registration Failed',
        'Could not create account. Please try a different username.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Fixed function to handle navigation back to Login screen
  const goToLogin = () => {
    // Use replace instead of navigate to avoid stacking screens
    navigation.replace('Login');
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView style={registerStyles.safeArea}>
        <StatusBar barStyle="light-content" backgroundColor={themeColors.primary} />

        <Animated.View
          style={[
            registerStyles.container,
            { transform: [{ translateY: logoPosition }] }
          ]}
        >
        {/* Background gradient */}
        <View style={registerStyles.backgroundGradient} />
        
        {/* Floating shapes - purely decorative */}
        <View style={[registerStyles.floatingShape, registerStyles.floatingShape1]} />
        <View style={[registerStyles.floatingShape, registerStyles.floatingShape2]} />
        <View style={[registerStyles.floatingShape, registerStyles.floatingShape3]} />
        
        {/* Logo section */}
        <View style={registerStyles.logoContainer}>
          <Image
            source={require('../assets/logo.png')}
            style={registerStyles.logo}
            resizeMode="contain"
            fadeDuration={0}
          />
          <Text style={registerStyles.appName}>Shake & Match</Text>
          <Text style={registerStyles.tagline}>Create a new account</Text>
        </View>

        {/* Form section with animation */}
        <Animated.View 
          style={[
            registerStyles.formContainer,
            { 
              opacity: formOpacity,
              transform: [
                { 
                  scale: formOpacity.interpolate({
                    inputRange: [0.97, 1],
                    outputRange: [0.98, 1]
                  }) 
                }
              ] 
            }
          ]}
        >
          {/* Username input */}
          <View style={registerStyles.fieldWrapper}>
            <View style={registerStyles.inputContainer}>
              <Ionicons name="person-outline" size={24} color={themeColors.primary} style={registerStyles.inputIcon} />
              <TextInput
                style={registerStyles.input}
                placeholder="Choose a Username"
                placeholderTextColor={themeColors.textLight}
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
                autoCorrect={false}
                spellCheck={false}
                returnKeyType="next"
                onSubmitEditing={() => passwordInputRef.current?.focus()}
                textContentType="username"
                autoComplete="username-new"
              />
            </View>
          </View>
          
          {/* Password input */}
          <View style={registerStyles.fieldWrapper}>
            <View style={registerStyles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={24} color={themeColors.primary} style={registerStyles.inputIcon} />
              <TextInput
                ref={passwordInputRef}
                style={[registerStyles.input, registerStyles.passwordInput]}
                placeholder="Password"
                placeholderTextColor={themeColors.textLight}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={true}
                autoCapitalize="none"
                autoCorrect={false}
                spellCheck={false}
                returnKeyType="next"
                onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                textContentType="newPassword"
                autoComplete="password-new"
                importantForAutofill="yes"
                passwordRules="minlength: 6;"
              />
            </View>
          </View>
          
          {/* Confirm Password input */}
          <View style={registerStyles.fieldWrapper}>
            <View style={registerStyles.inputContainer}>
              <Ionicons name="shield-checkmark-outline" size={24} color={themeColors.primary} style={registerStyles.inputIcon} />
              <TextInput
                ref={confirmPasswordInputRef}
                style={[registerStyles.input, registerStyles.passwordInput]}
                placeholder="Confirm Password"
                placeholderTextColor={themeColors.textLight}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={true}
                autoCapitalize="none"
                autoCorrect={false}
                spellCheck={false}
                returnKeyType="done"
                onSubmitEditing={handleRegister}
                textContentType="newPassword"
                autoComplete="password-new"
                importantForAutofill="yes"
                passwordRules="minlength: 6;"
              />
            </View>
          </View>
          
          <TouchableOpacity
            style={registerStyles.registerButton}
            onPress={handleRegister}
            disabled={isLoading}
            activeOpacity={0.85}
          >
            {isLoading ? (
              <ActivityIndicator color={themeColors.textWhite} />
            ) : (
              <>
                <Text style={registerStyles.registerButtonText}>Create Account</Text>
                <Ionicons name="arrow-forward" size={20} color={themeColors.textWhite} style={registerStyles.buttonIcon} />
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={registerStyles.loginLinkButton}
            onPress={goToLogin}
            activeOpacity={0.7}
          >
            <Text style={registerStyles.loginLinkText}>
              Already have an account? <Text style={registerStyles.loginLinkTextBold}>Sign In</Text>
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

// Styles are now imported from ScreensStyles.js

export default RegisterScreen;

