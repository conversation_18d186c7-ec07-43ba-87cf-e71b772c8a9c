const User = require('../models/User');

/**
 * Middleware to check if user has active premium subscription
 * Requires authentication middleware to be run first
 */
const requirePremium = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required.' });
    }

    // Refresh user data to get latest premium status
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(401).json({ error: 'User not found.' });
    }

    // Check if user has active premium subscription
    if (!user.isPremium) {
      return res.status(403).json({ 
        error: 'Premium subscription required.',
        message: 'This feature is only available to premium users.',
        premiumRequired: true
      });
    }

    // Update req.user with fresh data including premium status
    req.user = user;
    next();
  } catch (error) {
    console.error('Premium check error:', error);
    res.status(500).json({ error: 'Premium verification failed.' });
  }
};

/**
 * Middleware to check premium status and add to response (non-blocking)
 * Useful for endpoints that should work for both premium and non-premium users
 * but need to know premium status
 */
const checkPremiumStatus = async (req, res, next) => {
  try {
    if (req.user) {
      // Refresh user data to get latest premium status
      const user = await User.findById(req.user._id);
      if (user) {
        req.user = user;
        req.isPremium = user.isPremium;
        req.premiumDaysRemaining = user.premiumDaysRemaining;
      }
    }
    next();
  } catch (error) {
    console.error('Premium status check error:', error);
    // Don't fail the request, just continue without premium status
    next();
  }
};

/**
 * Utility function to get premium user statistics
 */
const getPremiumStats = async () => {
  try {
    const totalPremiumUsers = await User.countDocuments({ 'premium.isActive': true });
    const expiredPremiumUsers = await User.countDocuments({
      'premium.isActive': true,
      'premium.subscriptionEnd': { $lt: new Date() }
    });
    const activePremiumUsers = totalPremiumUsers - expiredPremiumUsers;
    
    // Get premium users by subscription type
    const monthlyUsers = await User.countDocuments({
      'premium.isActive': true,
      'premium.subscriptionType': 'monthly'
    });
    
    const yearlyUsers = await User.countDocuments({
      'premium.isActive': true,
      'premium.subscriptionType': 'yearly'
    });
    
    // Get users with custom location enabled
    const customLocationUsers = await User.countDocuments({
      'premium.customLocation.enabled': true
    });

    return {
      totalPremiumUsers,
      activePremiumUsers,
      expiredPremiumUsers,
      monthlyUsers,
      yearlyUsers,
      customLocationUsers
    };
  } catch (error) {
    console.error('Error getting premium stats:', error);
    throw error;
  }
};

module.exports = {
  requirePremium,
  checkPremiumStatus,
  getPremiumStats
};
