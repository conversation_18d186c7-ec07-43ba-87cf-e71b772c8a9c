// components/ThemedNavigator.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useThemeColors } from '../contexts/ThemeContext';

const Stack = createStackNavigator();

export const ThemedNavigator = ({ children, ...props }) => {
  const themeColors = useThemeColors();

  const screenOptions = {
    headerShown: true,
    headerStyle: {
      backgroundColor: themeColors.bgPrimary,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 0,
    },
    headerTitleStyle: {
      fontWeight: '600',
      color: themeColors.primary,
    },
    headerTintColor: themeColors.primary,
    // Enable swipe-back gesture
    gestureEnabled: true,
    gestureDirection: 'horizontal',
  };

  return (
    <Stack.Navigator 
      screenOptions={screenOptions}
      {...props}
    >
      {children}
    </Stack.Navigator>
  );
};

export default ThemedNavigator;
